{% extends "base.html" %}

{% block title %}Manage Students - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-3"></i>Manage Students</h2>
    <a href="{{ url_for('librarian_dashboard') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
    </a>
</div>

<!-- Search Form -->
<form method="GET" action="{{ url_for('librarian_student_search') }}" class="mb-4">
    <div class="input-group">
        <input type="text" class="form-control" name="q" placeholder="Search by name, user ID, or email" required>
        <button class="btn btn-primary" type="submit"><i class="fas fa-search me-2"></i>Search</button>
    </div>
</form>

{% if students %}
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Student List</h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Department</th>
                        <th>Course</th>
                        <th>Current Year</th>
                        <th>Validity</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr>
                        <td>{{ student.user_id }}</td>
                        <td>{{ student.name }}</td>
                        <td>{{ student.department }}</td>
                        <td>{{ student.course }}</td>
                        <td>{{ student.current_year }}</td>
                        <td>{{ student.validity_date.strftime('%Y-%m-%d') if student.validity_date else '' }}</td>
                        <td>
                            <a href="{{ url_for('librarian_student_details', student_id=student.id) }}" class="btn btn-info btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info mt-4">
    <i class="fas fa-info-circle me-2"></i>No students found.
</div>
{% endif %}
{% endblock %}
