{% extends "base.html" %}

{% block title %}Counter Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Resource Management -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#resourceSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-book me-2"></i>Resources
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="resourceSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>

        </div>
    </div>
</div>

<!-- User Management -->
<div class="nav-item">
    <a class="nav-link fw-bold text-purple" data-bs-toggle="collapse" href="#userSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-users me-2"></i>Users
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="userSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>View Students
            </a>

        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>

        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
        </div>
    </div>
</div>

<!-- Tools & Utilities -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#toolsSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-tools me-2"></i>Tools
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="toolsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_barcode_generator') }}">
                <i class="fas fa-barcode me-2"></i>Barcode Generator
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_label_printer') }}">
                <i class="fas fa-print me-2"></i>Label Printer
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_inventory_check') }}">
                <i class="fas fa-clipboard-check me-2"></i>Inventory Check
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_notifications') }}">
                <i class="fas fa-bell me-2"></i>Notifications
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-calculator me-3 text-primary"></i>Counter Report</h2>
        <p class="text-muted mb-0">Daily circulation statistics and counter performance metrics</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Counter Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="todayIssues">0</h4>
                <p class="text-muted mb-0 small">Today's Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-arrow-down fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="todayReturns">0</h4>
                <p class="text-muted mb-0 small">Today's Returns</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-sync fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="todayRenewals">0</h4>
                <p class="text-muted mb-0 small">Today's Renewals</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="todayFines">₹0</h4>
                <p class="text-muted mb-0 small">Today's Fines</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-primary text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Report Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-4">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-4">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-4">
                    <label for="operationType" class="form-label">Operation Type</label>
                    <select class="form-select" id="operationType" name="operationType">
                        <option value="">All Operations</option>
                        <option value="issue">Book Issues</option>
                        <option value="return">Book Returns</option>
                        <option value="renewal">Renewals</option>
                        <option value="fine">Fine Collection</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Counter Activity Table -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Daily Counter Activity</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="counterTable">
                <thead class="table-dark">
                    <tr>
                        <th>Date</th>
                        <th>Issues</th>
                        <th>Returns</th>
                        <th>Renewals</th>
                        <th>Fines Collected (₹)</th>
                        <th>Total Transactions</th>
                        <th>Peak Hour</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="fas fa-info-circle me-2"></i>No counter activity data available. Start using the system to see reports.
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default dates (today)
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
});

function generateReport() {
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        operationType: $('#operationType').val()
    };
    
    // Show loading
    $('#counterTable tbody').html('<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading counter data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadCounterData(filters);
    }, 1000);
}

function loadCounterData(filters) {
    // Since database is clean, show zero data
    const counterData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#todayIssues').text('0');
    $('#todayReturns').text('0');
    $('#todayRenewals').text('0');
    $('#todayFines').text('₹0');
    
    // Update counter table
    if (counterData.length === 0) {
        $('#counterTable tbody').html(`
            <tr>
                <td colspan="7" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No counter activity yet. Start issuing/returning books to see data.
                </td>
            </tr>
        `);
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(today.toISOString().split('T')[0]);
    generateReport();
}

function exportReport(format) {
    alert(`Exporting Counter Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
