{% extends "base.html" %}

{% block title %}Return Book - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_book') }}">
    <i class="fas fa-book-reader me-2"></i>Issue Book
</a>
<a class="nav-link active" href="{{ url_for('librarian_return_book') }}">
    <i class="fas fa-book me-2"></i>Return Book
</a>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4"><i class="fas fa-book me-2"></i>Return Book</h2>
    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ url_for('librarian_return_book') }}" class="row g-3">
                <div class="col-md-8">
                    <label for="issue_id" class="form-label">Select Issued Book</label>
                    <select class="form-select" id="issue_id" name="issue_id" required>
                        {% for issue in issues %}
                        <option value="{{ issue.issue_id }}">{{ issue.book.title }} ({{ issue.book.access_no }}) - {{ issue.student.name }} (Due: {{ issue.due_date.strftime('%Y-%m-%d') }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-success">Return Book</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
                        <table class="table table-bordered table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>Issue ID</th>
                                    <th>Book Title</th>
                                    <th>Student Name</th>
                                    <th>Issued On</th>
                                    <th>Due Date</th>
                                    <th>Return</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for issue in issues %}
                                <tr>
                                    <td>{{ issue.issue_id }}</td>
                                    <td>{{ issue.book.title }}</td>
                                    <td>{{ issue.student.name }}</td>
                                    <td>{{ issue.issue_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ issue.due_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <button type="submit" name="issue_id" value="{{ issue.issue_id }}" class="btn btn-success btn-sm">
                                            <i class="fas fa-check"></i> Return
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
                {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>No active book issues to return.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
