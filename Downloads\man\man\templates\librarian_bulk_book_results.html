{% extends "base.html" %}

{% block title %}Bulk Book Upload Results - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-upload me-3"></i>Bulk Book Upload Results</h2>
    <a href="{{ url_for('librarian_books') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Books
    </a>
</div>

<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Upload Summary</h5>
    </div>
    <div class="card-body">
        <p><strong>Total Records Processed:</strong> {{ results.total }}</p>
        <p><strong>Successful:</strong> <span class="text-success">{{ results.successful }}</span></p>
        <p><strong>Failed:</strong> <span class="text-danger">{{ results.failed }}</span></p>
        <p><strong>Skipped:</strong> <span class="text-warning">{{ results.skipped }}</span></p>
    </div>
</div>

{% if results.errors %}
<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Errors</h5>
    </div>
    <div class="card-body">
        <ul class="mb-0">
            {% for error in results.errors %}
            <li>{{ error }}</li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endif %}
{% endblock %}
