{% extends "base.html" %}

{% block title %}Missing Books Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-exclamation-triangle me-3 text-danger"></i>Missing Books Report</h2>
        <p class="text-muted mb-0">Track and manage missing or lost books</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
        <button class="btn btn-warning ms-2" onclick="markAsFound()">
            <i class="fas fa-check me-2"></i>Mark as Found
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">Missing Since</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category">
                    <option value="">All Categories</option>
                    <option value="textbook">Textbook</option>
                    <option value="reference">Reference</option>
                    <option value="fiction">Fiction</option>
                    <option value="journal">Journal</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status">
                    <option value="">All Status</option>
                    <option value="missing">Missing</option>
                    <option value="lost">Lost</option>
                    <option value="damaged">Damaged</option>
                    <option value="investigating">Under Investigation</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Missing</h6>
                        <h3 class="mb-0" id="totalMissing">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">This Month</h6>
                        <h3 class="mb-0" id="thisMonth">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Value</h6>
                        <h3 class="mb-0" id="totalValue">₹0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Recovered</h6>
                        <h3 class="mb-0" id="recovered">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Missing Books Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Missing Books</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="missingTable">
                <thead class="table-dark">
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Category</th>
                        <th>Last Seen</th>
                        <th>Missing Since</th>
                        <th>Value</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="missingTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadMissingData();
    
    // Set default dates (last 90 days)
    const today = new Date();
    const threeMonthsAgo = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(threeMonthsAgo.toISOString().split('T')[0]);
});

function loadMissingData() {
    // Show loading state
    $('#missingTableBody').html('<tr><td colspan="10" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                totalMissing: 15,
                thisMonth: 3,
                totalValue: 12500,
                recovered: 8
            },
            data: [
                {
                    id: '1',
                    accessNo: 'CS001',
                    title: 'Introduction to Computer Science',
                    author: 'John Smith',
                    category: 'textbook',
                    lastSeen: '2024-01-10',
                    missingSince: '2024-01-15',
                    value: 1200,
                    status: 'missing'
                },
                {
                    id: '2',
                    accessNo: 'MATH001',
                    title: 'Advanced Mathematics',
                    author: 'Jane Doe',
                    category: 'textbook',
                    lastSeen: '2024-01-08',
                    missingSince: '2024-01-12',
                    value: 800,
                    status: 'investigating'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#totalMissing').text(stats.totalMissing || 0);
    $('#thisMonth').text(stats.thisMonth || 0);
    $('#totalValue').text('₹' + (stats.totalValue || 0).toLocaleString());
    $('#recovered').text(stats.recovered || 0);
}

function populateTable(data) {
    const tbody = $('#missingTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="10" class="text-center">No missing books found</td></tr>');
        return;
    }
    
    data.forEach(function(book) {
        const row = `
            <tr>
                <td>
                    <input type="checkbox" class="book-checkbox" value="${book.id}">
                </td>
                <td><strong>${book.accessNo}</strong></td>
                <td>${book.title}</td>
                <td>${book.author}</td>
                <td><span class="badge bg-${getCategoryColor(book.category)}">${book.category}</span></td>
                <td>${book.lastSeen}</td>
                <td>${book.missingSince}</td>
                <td>₹${book.value}</td>
                <td><span class="badge bg-${getStatusColor(book.status)}">${book.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewBook('${book.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="markFound('${book.id}')">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="updateStatus('${book.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getCategoryColor(category) {
    const colors = {
        'textbook': 'primary',
        'reference': 'success',
        'fiction': 'info',
        'journal': 'warning'
    };
    return colors[category] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'missing': 'danger',
        'lost': 'dark',
        'damaged': 'warning',
        'investigating': 'info'
    };
    return colors[status] || 'secondary';
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.book-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function applyFilters() {
    loadMissingData();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#category').val('');
    $('#status').val('');
    loadMissingData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function markAsFound() {
    const selectedBooks = [];
    document.querySelectorAll('.book-checkbox:checked').forEach(checkbox => {
        selectedBooks.push(checkbox.value);
    });
    
    if (selectedBooks.length === 0) {
        alert('Please select books to mark as found');
        return;
    }
    
    if (confirm(`Mark ${selectedBooks.length} book(s) as found?`)) {
        alert('Books marked as found: ' + selectedBooks.join(', '));
        loadMissingData(); // Reload data
    }
}

function viewBook(bookId) {
    alert('View book details: ' + bookId);
}

function markFound(bookId) {
    if (confirm('Mark this book as found?')) {
        alert('Book marked as found: ' + bookId);
        loadMissingData(); // Reload data
    }
}

function updateStatus(bookId) {
    alert('Update status for book: ' + bookId);
}
</script>
{% endblock %}
