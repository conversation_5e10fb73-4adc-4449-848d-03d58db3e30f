{% extends "base.html" %}

{% block title %}Access Register Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-credit-card me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 active" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-sign-in-alt me-3 text-success"></i>Access Register Report</h2>
        <p class="text-muted mb-0">Complete access log of all library users with entry/exit timestamps</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Access Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="accessType" class="form-label">Access Type</label>
                    <select class="form-select" id="accessType" name="accessType">
                        <option value="">All Access</option>
                        <option value="entry">Entry Only</option>
                        <option value="exit">Exit Only</option>
                        <option value="both">Entry & Exit</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="searchUser" class="form-label">Search User</label>
                    <input type="text" class="form-control" id="searchUser" placeholder="Enter name or ID">
                </div>
                <div class="col-md-6">
                    <label for="timeRange" class="form-label">Time Range</label>
                    <select class="form-select" id="timeRange" name="timeRange">
                        <option value="">All Day</option>
                        <option value="morning">Morning (6 AM - 12 PM)</option>
                        <option value="afternoon">Afternoon (12 PM - 6 PM)</option>
                        <option value="evening">Evening (6 PM - 10 PM)</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-success" onclick="applyFilters()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-undo me-2"></i>Reset Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-stat-card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Total Access</h6>
                        <h3 class="mb-0 fw-bold" id="totalEntries">0</h3>
                        <small class="opacity-75">All time entries</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-door-open fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-stat-card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Currently Inside</h6>
                        <h3 class="mb-0 fw-bold" id="currentlyInside">0</h3>
                        <small class="opacity-75">Active users</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-stat-card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Avg Duration</h6>
                        <h3 class="mb-0 fw-bold" id="avgDuration">0h 0m</h3>
                        <small class="opacity-75">Per session</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-stopwatch fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-stat-card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-1">Peak Hour</h6>
                        <h3 class="mb-0 fw-bold" id="peakTime">--:--</h3>
                        <small class="opacity-75">Busiest time</small>
                    </div>
                    <div class="stat-icon">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Access Register Table -->
<div class="card autolib-card">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i>Access Register Records</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover autolib-table" id="accessTable">
                <thead class="table-dark">
                    <tr>
                        <th><i class="fas fa-sign-in-alt me-1"></i>Entry Time</th>
                        <th><i class="fas fa-sign-out-alt me-1"></i>Exit Time</th>
                        <th><i class="fas fa-id-card me-1"></i>User ID</th>
                        <th><i class="fas fa-user me-1"></i>Name</th>
                        <th><i class="fas fa-building me-1"></i>Department</th>
                        <th><i class="fas fa-clock me-1"></i>Duration</th>
                        <th><i class="fas fa-info-circle me-1"></i>Status</th>
                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                    </tr>
                </thead>
                <tbody id="accessTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    // Set default dates (last 7 days)
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastWeek.toISOString().split('T')[0]);

    // Initialize empty table
    $('#accessTableBody').html('<tr><td colspan="8" class="text-center">Click "Generate Report" to load data</td></tr>');
});

function loadAccessData() {
    // Show loading state
    $('#accessTableBody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');

    // Get filter values
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        accessType: $('#accessType').val(),
        department: $('#department').val(),
        searchUser: $('#searchUser').val(),
        timeRange: $('#timeRange').val()
    };

    // TODO: Replace with actual API call
    // Example: fetch('/api/librarian/access-register-data', { method: 'POST', body: JSON.stringify(filters) })
    //   .then(response => response.json())
    //   .then(data => {
    //       updateStatistics(data.statistics);
    //       populateTable(data.records);
    //   });

    // For now, show empty state
    setTimeout(function() {
        $('#accessTableBody').html('<tr><td colspan="8" class="text-center">No data available. Please implement API endpoint.</td></tr>');
        updateStatistics({});
    }, 500);
}

function updateStatistics(stats) {
    $('#totalEntries').text(stats.totalEntries || 0);
    $('#currentlyInside').text(stats.currentlyInside || 0);
    $('#avgDuration').text(stats.avgDuration || '0h 0m');
    $('#peakTime').text(stats.peakTime || '--:--');
}

function populateTable(data) {
    const tbody = $('#accessTableBody');
    tbody.empty();

    if (!data || data.length === 0) {
        tbody.html('<tr><td colspan="8" class="text-center">No access records found</td></tr>');
        return;
    }

    data.forEach(function(record) {
        const row = `
            <tr>
                <td>${record.entryTime || '-'}</td>
                <td>${record.exitTime || '-'}</td>
                <td>${record.userId || '-'}</td>
                <td>${record.name || '-'}</td>
                <td>${record.department || '-'}</td>
                <td>${record.duration || '-'}</td>
                <td><span class="badge bg-${record.status === 'Inside' ? 'success' : 'secondary'}">${record.status || 'Unknown'}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${record.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function applyFilters() {
    loadAccessData();
}

function resetFilters() {
    $('#filterForm')[0].reset();
    // Set default dates again
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastWeek.toISOString().split('T')[0]);
}

function exportReport(format) {
    // TODO: Implement actual export functionality
    const filters = {
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        accessType: $('#accessType').val(),
        department: $('#department').val(),
        searchUser: $('#searchUser').val(),
        timeRange: $('#timeRange').val(),
        format: format
    };

    console.log('Export request:', filters);
    alert('Export functionality needs to be implemented');
}

function viewDetails(recordId) {
    // TODO: Implement view details functionality
    console.log('View details for record:', recordId);
    alert('View details functionality needs to be implemented');
}
</script>
{% endblock %}
