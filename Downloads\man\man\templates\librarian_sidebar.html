<!-- Librarian <PERSON>bar Navigation -->
<!-- Dashboard -->
<a class="nav-link {{ 'active' if request.endpoint == 'librarian_dashboard' else '' }}" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_books' else '' }}" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_ebooks' else '' }}" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_students' else '' }}" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_news_clippings' else '' }}" href="{{ url_for('librarian_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_circulation_counter' else '' }}" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_issue_history' else '' }}" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_bulk_operations' else '' }}" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_payment_management' else '' }}" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_binding_management' else '' }}" href="{{ url_for('librarian_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_fine_management' else '' }}" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_library_connection_report' else '' }}" href="{{ url_for('librarian_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_access_register_report' else '' }}" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_bibliography_report' else '' }}" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_counter_report' else '' }}" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_statistics_report' else '' }}" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_binding_report' else '' }}" href="{{ url_for('librarian_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_database_report' else '' }}" href="{{ url_for('librarian_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_member_report' else '' }}" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_resource_report' else '' }}" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_no_dues_report' else '' }}" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_qb_report' else '' }}" href="{{ url_for('librarian_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_transfer_report' else '' }}" href="{{ url_for('librarian_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_missing_report' else '' }}" href="{{ url_for('librarian_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_news_clipping_report' else '' }}" href="{{ url_for('librarian_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- Tools & Utilities -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#toolsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-tools me-2"></i>Tools
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="toolsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_barcode_generator' else '' }}" href="{{ url_for('librarian_barcode_generator') }}">
                <i class="fas fa-barcode me-2"></i>Barcode Generator
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_label_printer' else '' }}" href="{{ url_for('librarian_label_printer') }}">
                <i class="fas fa-print me-2"></i>Label Printer
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_inventory_check' else '' }}" href="{{ url_for('librarian_inventory_check') }}">
                <i class="fas fa-clipboard-check me-2"></i>Inventory Check
            </a>
            <a class="nav-link py-1 {{ 'active' if request.endpoint == 'librarian_notifications' else '' }}" href="{{ url_for('librarian_notifications') }}">
                <i class="fas fa-bell me-2"></i>Notifications
            </a>
        </div>
    </div>
</div>
