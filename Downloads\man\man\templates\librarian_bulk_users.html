{% extends "base.html" %}

{% block title %}Bulk Create Users - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>Manage Students
</a>
<a class="nav-link active" href="{{ url_for('librarian_bulk_users') }}">
    <i class="fas fa-upload me-2"></i>Bulk Create Users
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_return') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-upload me-3"></i>Bulk Create Users</h2>
    <a href="{{ url_for('librarian_students') }}" class="btn btn-secondary btn-custom">
        <i class="fas fa-arrow-left me-2"></i>Back to Students
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Upload Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cloud-upload-alt me-2"></i>Upload Excel File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">
                            <i class="fas fa-file-excel me-2"></i>Select Excel File
                        </label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Upload an Excel file (.xlsx or .xls) with student data
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-upload me-2"></i>Upload & Create Users
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Template Download -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i>Excel Template</h5>
            </div>
            <div class="card-body">
                <p class="mb-3">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    Download the template to ensure your Excel file has the correct format.
                </p>
                
                <div class="table-responsive mb-3">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>user_id</th>
                                <th>username</th>
                                <th>department</th>
                                <th>designation</th>
                                <th>course</th>
                                <th>dob</th>
                                <th>current_year</th>
                                <th>validity_date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-muted">
                                <td>2024001</td>
                                <td>John Doe</td>
                                <td>CSE</td>
                                <td>Student</td>
                                <td>CSE</td>
                                <td>1995-01-15</td>
                                <td>3</td>
                                <td>2026-12-31</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <a href="{{ url_for('librarian_bulk_users_template') }}" class="btn btn-outline-success">
                    <i class="fas fa-download me-2"></i>Download Template
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Important Notes</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-key me-2"></i>Password Generation:</h6>
                    <p class="small mb-2">Passwords are automatically generated using the format: <strong>user_id + name</strong></p>
                    <p class="small mb-0">Example: If user_id is "123" and name is "John Doe", password will be "123johndoe"</p>
                </div>
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-rules me-2"></i>Required Fields:</h6>
                    <ul class="mb-0 small">
                        <li><strong>user_id:</strong> Unique identifier</li>
                        <li><strong>username:</strong> Full name</li>
                        <li><strong>department:</strong> CSE, IT, ECE, etc.</li>
                        <li><strong>designation:</strong> Student or Staff</li>
                        <li><strong>course:</strong> Course name</li>
                        <li><strong>dob:</strong> Date of birth (YYYY-MM-DD)</li>
                        <li><strong>current_year:</strong> Academic year</li>
                        <li><strong>validity_date:</strong> Account expiry date</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Department List -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-university me-2"></i>Valid Departments</h6>
            </div>
            <div class="card-body">
                <div class="row small">
                    {% for dept_code, dept_name in departments %}
                    <div class="col-6 mb-1">
                        <span class="badge bg-light text-dark">{{ dept_code }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
