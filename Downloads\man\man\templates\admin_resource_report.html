{% extends "base.html" %}

{% block title %}Resource Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-archive me-3 text-info"></i>Resource Utilization Report</h2>
        <p class="text-muted mb-0">Available vs issued resources, utilization analysis, and acquisition reports</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Resource Overview Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-book fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalResources">0</h4>
                <p class="text-muted mb-0 small">Total Resources</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="availableResources">0</h4>
                <p class="text-muted mb-0 small">Available</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="issuedResources">0</h4>
                <p class="text-muted mb-0 small">Currently Issued</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-percentage fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="utilizationRate">0%</h4>
                <p class="text-muted mb-0 small">Utilization Rate</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-plus fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="newAcquisitions">0</h4>
                <p class="text-muted mb-0 small">New This Month</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="totalValue">₹0</h4>
                <p class="text-muted mb-0 small">Total Value</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Resource Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="resourceType" class="form-label">Resource Type</label>
                    <select class="form-select" id="resourceType" name="resourceType">
                        <option value="">All Resources</option>
                        <option value="books">Physical Books</option>
                        <option value="ebooks">E-Books</option>
                        <option value="journals">Journals</option>
                        <option value="reference">Reference Materials</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="subject" class="form-label">Subject</label>
                    <select class="form-select" id="subject" name="subject">
                        <option value="">All Subjects</option>
                        <option value="Computer Science">Computer Science</option>
                        <option value="Mathematics">Mathematics</option>
                        <option value="Physics">Physics</option>
                        <option value="Chemistry">Chemistry</option>
                        <option value="Engineering">Engineering</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="utilizationFilter" class="form-label">Utilization Level</label>
                    <select class="form-select" id="utilizationFilter" name="utilizationFilter">
                        <option value="">All Levels</option>
                        <option value="high">High (>80%)</option>
                        <option value="medium">Medium (40-80%)</option>
                        <option value="low">Low (<40%)</option>
                        <option value="unused">Never Used</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="acquisitionYear" class="form-label">Acquisition Year</label>
                    <select class="form-select" id="acquisitionYear" name="acquisitionYear">
                        <option value="">All Years</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="older">Before 2021</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-info" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-primary ms-2" onclick="showUtilizationChart()">
                        <i class="fas fa-chart-bar me-2"></i>Utilization Chart
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Resource Analysis Tabs -->
<ul class="nav nav-tabs mb-4" id="resourceTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="resource-overview-tab" data-bs-toggle="tab" data-bs-target="#resource-overview" type="button" role="tab">
            <i class="fas fa-list me-2"></i>Resource Overview
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="utilization-analysis-tab" data-bs-toggle="tab" data-bs-target="#utilization-analysis" type="button" role="tab">
            <i class="fas fa-chart-pie me-2"></i>Utilization Analysis
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="acquisition-report-tab" data-bs-toggle="tab" data-bs-target="#acquisition-report" type="button" role="tab">
            <i class="fas fa-plus-circle me-2"></i>Acquisition Report
        </button>
    </li>
</ul>

<div class="tab-content" id="resourceTabContent">
    <!-- Resource Overview Tab -->
    <div class="tab-pane fade show active" id="resource-overview" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Resource Inventory</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="resourceTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Resource ID</th>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Subject</th>
                                <th>Total Copies</th>
                                <th>Available</th>
                                <th>Issued</th>
                                <th>Utilization %</th>
                                <th>Last Issued</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No resources available. Add books to see resource data.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Utilization Analysis Tab -->
    <div class="tab-pane fade" id="utilization-analysis" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Subject-wise Utilization</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="subjectUtilizationChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Monthly Utilization Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="utilizationTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performing Resources</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Resource</th>
                                        <th>Times Issued</th>
                                        <th>Utilization Rate</th>
                                        <th>Popularity Score</th>
                                    </tr>
                                </thead>
                                <tbody id="topResourcesTable">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            No utilization data available
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Acquisition Report Tab -->
    <div class="tab-pane fade" id="acquisition-report" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Resource Acquisition History</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-success" id="acquisitionsThisYear">0</h4>
                                <p class="text-muted mb-0">This Year</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-primary" id="acquisitionsThisMonth">0</h4>
                                <p class="text-muted mb-0">This Month</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h4 class="text-warning" id="budgetUtilized">₹0</h4>
                                <p class="text-muted mb-0">Budget Utilized</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Resource</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Cost per Unit</th>
                                <th>Total Cost</th>
                                <th>Vendor</th>
                            </tr>
                        </thead>
                        <tbody id="acquisitionTable">
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No acquisition records available.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Load initial data
    generateReport();
    
    // Initialize charts
    initializeUtilizationCharts();
});

function generateReport() {
    const filters = {
        resourceType: $('#resourceType').val(),
        subject: $('#subject').val(),
        utilizationFilter: $('#utilizationFilter').val(),
        acquisitionYear: $('#acquisitionYear').val()
    };
    
    // Show loading
    $('#resourceTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading resource data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadResourceData(filters);
    }, 1000);
}

function loadResourceData(filters) {
    // Since database is clean, show zero data
    const resourceData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalResources').text('0');
    $('#availableResources').text('0');
    $('#issuedResources').text('0');
    $('#utilizationRate').text('0%');
    $('#newAcquisitions').text('0');
    $('#totalValue').text('₹0');
    $('#acquisitionsThisYear').text('0');
    $('#acquisitionsThisMonth').text('0');
    $('#budgetUtilized').text('₹0');
    
    // Update resource table
    if (resourceData.length === 0) {
        $('#resourceTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No resources available. Add books to see resource data.
                </td>
            </tr>
        `);
        
        $('#topResourcesTable').html(`
            <tr>
                <td colspan="5" class="text-center text-muted">
                    No utilization data available
                </td>
            </tr>
        `);
        
        $('#acquisitionTable').html(`
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>No acquisition records available.
                </td>
            </tr>
        `);
    }
    
    // Update charts with zero data
    updateUtilizationCharts([]);
}

function initializeUtilizationCharts() {
    // Subject-wise utilization chart
    const ctx1 = document.getElementById('subjectUtilizationChart').getContext('2d');
    window.subjectChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['Computer Science', 'Mathematics', 'Physics', 'Chemistry', 'Engineering'],
            datasets: [{
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Utilization trend chart
    const ctx2 = document.getElementById('utilizationTrendChart').getContext('2d');
    window.trendChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Utilization Rate (%)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

function updateUtilizationCharts(data) {
    if (window.subjectChart && data.length === 0) {
        window.subjectChart.data.datasets[0].data = [0, 0, 0, 0, 0];
        window.subjectChart.update();
    }
    
    if (window.trendChart && data.length === 0) {
        window.trendChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.trendChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    generateReport();
}

function showUtilizationChart() {
    $('#utilization-analysis-tab').click();
}

function exportReport(format) {
    alert(`Exporting Resource Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
