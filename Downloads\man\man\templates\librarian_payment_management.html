{% extends "base.html" %}

{% block title %}Payment Management - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>View Students
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-money-bill-wave me-3"></i>Payment Management</h2>
    <div>
        <button class="btn btn-outline-primary btn-custom" onclick="showDailyReport()">
            <i class="fas fa-chart-bar me-2"></i>Daily Report
        </button>
    </div>
</div>

<!-- Payment Type Tabs -->
<ul class="nav nav-tabs mb-4" id="paymentTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="collect-payment-tab" data-bs-toggle="tab" data-bs-target="#collect-payment" type="button" role="tab">
            <i class="fas fa-hand-holding-usd me-2"></i>Collect Payment
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="payment-history-tab" data-bs-toggle="tab" data-bs-target="#payment-history" type="button" role="tab">
            <i class="fas fa-history me-2"></i>Payment History
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="outstanding-fines-tab" data-bs-toggle="tab" data-bs-target="#outstanding-fines" type="button" role="tab">
            <i class="fas fa-exclamation-triangle me-2"></i>Outstanding Fines
        </button>
    </li>
</ul>

<div class="tab-content" id="paymentTabContent">
    <!-- Collect Payment Tab -->
    <div class="tab-pane fade show active" id="collect-payment" role="tabpanel">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-hand-holding-usd me-2"></i>Collect Payment</h5>
                    </div>
                    <div class="card-body">
                        <form id="paymentForm">
                            <!-- Student Search -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="paymentStudentSearch" class="form-label">Student ID/Name <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="paymentStudentSearch" placeholder="Scan or type student ID" required>
                                        <button type="button" class="btn btn-outline-secondary" onclick="searchPaymentStudent()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="paymentStudentInfo" class="alert alert-info" style="display: none;">
                                <h6><i class="fas fa-user me-2"></i>Student Information</h6>
                                <div id="paymentStudentDetails"></div>
                            </div>
                            
                            <!-- Outstanding Fines -->
                            <div id="outstandingFines" class="card mb-3" style="display: none;">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Outstanding Fines</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Book</th>
                                                    <th>Due Date</th>
                                                    <th>Days Overdue</th>
                                                    <th>Fine Amount</th>
                                                    <th>Select</th>
                                                </tr>
                                            </thead>
                                            <tbody id="finesTableBody">
                                                <!-- Dynamic content -->
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-end">
                                        <strong>Total Outstanding: ₹<span id="totalOutstanding">0.00</span></strong>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Payment Details -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="paymentType" class="form-label">Payment Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="paymentType" required>
                                        <option value="">Select Payment Type</option>
                                        <option value="Fine">Overdue Fine</option>
                                        <option value="Damage">Damage Charge</option>
                                        <option value="Lost">Lost Book Charge</option>
                                        <option value="Fee">Membership Fee</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="paymentAmount" class="form-label">Amount (₹) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="paymentAmount" step="0.01" min="0" required>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="paymentMethod" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                    <select class="form-select" id="paymentMethod" required>
                                        <option value="Cash">Cash</option>
                                        <option value="Card">Card</option>
                                        <option value="UPI">UPI</option>
                                        <option value="Online">Online Transfer</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="paymentDescription" class="form-label">Description</label>
                                    <input type="text" class="form-control" id="paymentDescription" placeholder="Payment description">
                                </div>
                            </div>
                            
                            <!-- Payment Summary -->
                            <div class="card mb-3 bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-receipt me-2"></i>Payment Summary</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Amount: ₹<span id="summaryAmount">0.00</span></strong>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Method: <span id="summaryMethod">-</span></strong>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Receipt #: <span id="receiptNumber">-</span></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-2"></i>Process Payment & Generate Receipt
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Payment Info</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">Current Date/Time</small>
                            <div id="currentDateTime" class="fw-bold"></div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Processed By</small>
                            <div class="fw-bold">{{ session.user_name }}</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Fine Rate</small>
                            <div class="fw-bold">₹2 per day</div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">Today's Collections</small>
                            <div class="fw-bold text-success" id="todayCollections">₹0.00</div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>Quick Calculator</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <label for="calcDays" class="form-label">Days Overdue</label>
                            <input type="number" class="form-control" id="calcDays" min="0" onchange="calculateFine()">
                        </div>
                        <div class="mb-2">
                            <label for="calcRate" class="form-label">Rate per Day (₹)</label>
                            <input type="number" class="form-control" id="calcRate" value="2" step="0.01" onchange="calculateFine()">
                        </div>
                        <div class="mb-2">
                            <strong>Calculated Fine: ₹<span id="calculatedFine">0.00</span></strong>
                        </div>
                        <button class="btn btn-outline-primary btn-sm w-100" onclick="useCalculatedFine()">
                            <i class="fas fa-arrow-down me-2"></i>Use This Amount
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment History Tab -->
    <div class="tab-pane fade" id="payment-history" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Payment History</h5>
                <div>
                    <input type="date" class="form-control d-inline-block" id="historyDate" style="width: auto;">
                    <button class="btn btn-outline-primary btn-sm ms-2" onclick="filterPaymentHistory()">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Receipt #</th>
                                <th>Date</th>
                                <th>Student</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Method</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="paymentHistoryBody">
                            <tr>
                                <td>RCP001</td>
                                <td>2024-01-15</td>
                                <td>John Doe (CS001)</td>
                                <td>Fine</td>
                                <td>₹20.00</td>
                                <td>Cash</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="printReceipt('RCP001')">
                                        <i class="fas fa-print"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Outstanding Fines Tab -->
    <div class="tab-pane fade" id="outstanding-fines" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Outstanding Fines</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Department</th>
                                <th>Book</th>
                                <th>Due Date</th>
                                <th>Days Overdue</th>
                                <th>Fine Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>John Doe (CS001)</td>
                                <td>CSE</td>
                                <td>Sample Book</td>
                                <td>2024-01-01</td>
                                <td>14</td>
                                <td>₹28.00</td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="collectFine('CS001', 28)">
                                        <i class="fas fa-money-bill-wave"></i> Collect
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="text-end mt-3">
                    <strong>Total Outstanding Fines: ₹<span id="totalOutstandingFines">28.00</span></strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Receipt Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Receipt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="receiptContent">
                <!-- Receipt content will be generated here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printReceipt()">
                    <i class="fas fa-print me-2"></i>Print Receipt
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Update current date/time
    function updateDateTime() {
        const now = new Date();
        $('#currentDateTime').text(now.toLocaleString());
    }
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Generate receipt number
    generateReceiptNumber();
    
    // Load today's collections
    loadTodayCollections();
    
    // Update payment summary when amount or method changes
    $('#paymentAmount, #paymentMethod').on('change', updatePaymentSummary);
});

function generateReceiptNumber() {
    const now = new Date();
    const receiptNo = 'RCP' + now.getFullYear() + (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0') + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    $('#receiptNumber').text(receiptNo);
}

function searchPaymentStudent() {
    const query = $('#paymentStudentSearch').val();
    if (query) {
        $('#paymentStudentInfo').show();
        $('#paymentStudentDetails').html('<strong>John Doe</strong><br>ID: ' + query + '<br>Department: CSE<br>Contact: <EMAIL>');
        showOutstandingFines();
    }
}

function showOutstandingFines() {
    const fines = [
        { book: 'Sample Book 1', dueDate: '2024-01-01', daysOverdue: 14, fine: 28.00 },
        { book: 'Sample Book 2', dueDate: '2024-01-05', daysOverdue: 10, fine: 20.00 }
    ];
    
    const tbody = $('#finesTableBody');
    tbody.empty();
    
    let total = 0;
    fines.forEach((fine, index) => {
        total += fine.fine;
        tbody.append(`
            <tr>
                <td>${fine.book}</td>
                <td>${fine.dueDate}</td>
                <td>${fine.daysOverdue}</td>
                <td>₹${fine.fine.toFixed(2)}</td>
                <td>
                    <input type="checkbox" class="form-check-input fine-checkbox" 
                           data-amount="${fine.fine}" onchange="updateSelectedFines()">
                </td>
            </tr>
        `);
    });
    
    $('#totalOutstanding').text(total.toFixed(2));
    $('#outstandingFines').show();
}

function updateSelectedFines() {
    let selectedTotal = 0;
    $('.fine-checkbox:checked').each(function() {
        selectedTotal += parseFloat($(this).data('amount'));
    });
    $('#paymentAmount').val(selectedTotal.toFixed(2));
    updatePaymentSummary();
}

function updatePaymentSummary() {
    const amount = $('#paymentAmount').val() || '0.00';
    const method = $('#paymentMethod').val() || '-';
    
    $('#summaryAmount').text(parseFloat(amount).toFixed(2));
    $('#summaryMethod').text(method);
}

function calculateFine() {
    const days = parseInt($('#calcDays').val()) || 0;
    const rate = parseFloat($('#calcRate').val()) || 2;
    const fine = days * rate;
    $('#calculatedFine').text(fine.toFixed(2));
}

function useCalculatedFine() {
    const fine = $('#calculatedFine').text();
    $('#paymentAmount').val(fine);
    updatePaymentSummary();
}

function loadTodayCollections() {
    $('#todayCollections').text('₹150.00');
}

$('#paymentForm').on('submit', function(e) {
    e.preventDefault();
    
    const paymentData = {
        studentId: $('#paymentStudentSearch').val(),
        amount: $('#paymentAmount').val(),
        type: $('#paymentType').val(),
        method: $('#paymentMethod').val(),
        description: $('#paymentDescription').val(),
        receiptNumber: $('#receiptNumber').text(),
        processedBy: '{{ session.user_name }}'
    };
    
    processPayment(paymentData);
});

function processPayment(paymentData) {
    generateReceipt(paymentData);
    alert('Payment processed successfully! Receipt generated.');
    
    $('#paymentForm')[0].reset();
    $('#paymentStudentInfo').hide();
    $('#outstandingFines').hide();
    generateReceiptNumber();
}

function generateReceipt(paymentData) {
    const receiptHTML = `
        <div class="receipt-container" style="font-family: monospace; max-width: 400px; margin: 0 auto;">
            <div class="text-center mb-3">
                <h4>SMART LIBRARY MANAGEMENT</h4>
                <p class="mb-1">Payment Receipt</p>
                <hr>
            </div>
            
            <div class="receipt-details">
                <div class="row mb-2">
                    <div class="col-6"><strong>Receipt #:</strong></div>
                    <div class="col-6">${paymentData.receiptNumber}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Date:</strong></div>
                    <div class="col-6">${new Date().toLocaleDateString()}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Time:</strong></div>
                    <div class="col-6">${new Date().toLocaleTimeString()}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Student ID:</strong></div>
                    <div class="col-6">${paymentData.studentId}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Payment Type:</strong></div>
                    <div class="col-6">${paymentData.type}</div>
                </div>
                <div class="row mb-2">
                    <div class="col-6"><strong>Method:</strong></div>
                    <div class="col-6">${paymentData.method}</div>
                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-6"><strong>AMOUNT PAID:</strong></div>
                    <div class="col-6"><strong>₹${parseFloat(paymentData.amount).toFixed(2)}</strong></div>
                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-6"><strong>Processed By:</strong></div>
                    <div class="col-6">${paymentData.processedBy}</div>
                </div>
            </div>
            
            <div class="text-center mt-3">
                <p class="small">Thank you for your payment!</p>
                <p class="small">Keep this receipt for your records</p>
            </div>
        </div>
    `;
    
    $('#receiptContent').html(receiptHTML);
    $('#receiptModal').modal('show');
}

function printReceipt(receiptNumber) {
    if (receiptNumber) {
        console.log('Printing receipt:', receiptNumber);
    } else {
        const printContent = $('#receiptContent').html();
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Payment Receipt</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { padding: 20px; }
                        @media print { 
                            body { margin: 0; padding: 10px; }
                            .btn { display: none; }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

function collectFine(studentId, amount) {
    $('#paymentStudentSearch').val(studentId);
    $('#paymentAmount').val(amount);
    $('#paymentType').val('Fine');
    searchPaymentStudent();
    $('#collect-payment-tab').click();
}

function showDailyReport() {
    alert('Daily payment report functionality would be implemented here.');
}
</script>
{% endblock %}
