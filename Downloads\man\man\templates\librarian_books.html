{% extends "base.html" %}

{% block title %}Manage Books - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-book me-3"></i>Manage Books</h2>
    <div>
        <a href="{{ url_for('librarian_bulk_books') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-file-upload me-2"></i>Bulk Add Books
        </a>
        <a href="{{ url_for('librarian_add_book') }}" class="btn btn-primary btn-custom">
            <i class="fas fa-plus me-2"></i>Add New Book
        </a>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="searchBooks" placeholder="Search books by title, author, or access number...">
            <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterDepartment">
            <option value="">All Departments</option>
            {% for dept_code, dept_name in departments %}
            <option value="{{ dept_code }}">{{ dept_code }} - {{ dept_name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterCategory">
            <option value="">All Categories</option>
            <option value="Textbook">Textbook</option>
            <option value="Reference">Reference Book</option>
            <option value="Laboratory Manual">Laboratory Manual</option>
            <option value="Journal">Journal</option>
            <option value="Magazine">Magazine</option>
            <option value="Thesis">Thesis</option>
            <option value="Project Report">Project Report</option>
        </select>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-book fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ total_books }}</h4>
                <p class="mb-0">Total Books</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ available_books }}</h4>
                <p class="mb-0">Available</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-hand-holding fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ issued_books }}</h4>
                <p class="mb-0">Issued</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-university fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ total_departments }}</h4>
                <p class="mb-0">Departments</p>
            </div>
        </div>
    </div>
</div>

<!-- Books Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Books List</h5>
    </div>
    <div class="card-body">
        {% if books %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="booksTable">
                <thead class="table-dark">
                    <tr>
                        <th>Access No.</th>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Publisher</th>
                        <th>Subject</th>
                        <th>Department</th>
                        <th>Category</th>
                        <th>Location</th>
                        <th>Copies</th>
                        <th>Available</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for book in books %}
                    <tr>
                        <td><strong>{{ book.access_no }}</strong></td>
                        <td>{{ book.title }}</td>
                        <td>{{ book.author }}</td>
                        <td>{{ book.publisher }}</td>
                        <td>{{ book.subject }}</td>
                        <td>
                            <span class="badge bg-info">{{ book.department }}</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ book.category }}</span>
                        </td>
                        <td>{{ book.location }}</td>
                        <td class="text-center">{{ book.copies }}</td>
                        <td class="text-center">
                            {% set available = book.copies - (book.issued_count or 0) %}
                            {% if available > 0 %}
                                <span class="badge bg-success">{{ available }}</span>
                            {% else %}
                                <span class="badge bg-danger">0</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('librarian_edit_book', book_id=book.book_id) }}" 
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteBook({{ book.book_id }}, '{{ book.title }}')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No books found</h5>
            <p class="text-muted">Start by adding some books to the library.</p>
            <a href="{{ url_for('librarian_add_book') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Book
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the book "<span id="bookTitle"></span>"?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This action cannot be undone. All associated data will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete Book</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let bookToDelete = null;

function deleteBook(bookId, bookTitle) {
    bookToDelete = bookId;
    document.getElementById('bookTitle').textContent = bookTitle;
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (bookToDelete) {
        fetch(`/librarian/books/delete/${bookToDelete}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting book: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error deleting book: ' + error.message);
        });
        
        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        deleteModal.hide();
    }
});

// Search and filter functionality
document.getElementById('searchBooks').addEventListener('input', function() {
    filterTable();
});

document.getElementById('filterDepartment').addEventListener('change', function() {
    filterTable();
});

document.getElementById('filterCategory').addEventListener('change', function() {
    filterTable();
});

function filterTable() {
    const searchTerm = document.getElementById('searchBooks').value.toLowerCase();
    const departmentFilter = document.getElementById('filterDepartment').value;
    const categoryFilter = document.getElementById('filterCategory').value;
    const table = document.getElementById('booksTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.getElementsByTagName('td');
        let showRow = true;

        // Search filter
        if (searchTerm) {
            const accessNo = cells[0].textContent.toLowerCase();
            const title = cells[1].textContent.toLowerCase();
            const author = cells[2].textContent.toLowerCase();
            
            if (!accessNo.includes(searchTerm) && 
                !title.includes(searchTerm) && 
                !author.includes(searchTerm)) {
                showRow = false;
            }
        }

        // Department filter
        if (departmentFilter && showRow) {
            const department = cells[5].textContent.trim();
            if (!department.includes(departmentFilter)) {
                showRow = false;
            }
        }

        // Category filter
        if (categoryFilter && showRow) {
            const category = cells[6].textContent.trim();
            if (!category.includes(categoryFilter)) {
                showRow = false;
            }
        }

        row.style.display = showRow ? '' : 'none';
    }
}
</script>
{% endblock %}
