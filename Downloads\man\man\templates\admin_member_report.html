{% extends "base.html" %}

{% block title %}Member Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-users me-3 text-success"></i>Member Report</h2>
        <p class="text-muted mb-0">Active/inactive members, department-wise membership, and activity analysis</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Member Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="memberStatus" class="form-label">Member Status</label>
                    <select class="form-select" id="memberStatus" name="memberStatus">
                        <option value="">All Members</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="suspended">Suspended</option>
                        <option value="graduated">Graduated</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="department" class="form-label">Department</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">All Departments</option>
                        <option value="CSE">Computer Science</option>
                        <option value="IT">Information Technology</option>
                        <option value="ECE">Electronics & Communication</option>
                        <option value="EEE">Electrical & Electronics</option>
                        <option value="MECH">Mechanical</option>
                        <option value="CIVIL">Civil</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="yearOfStudy" class="form-label">Year of Study</label>
                    <select class="form-select" id="yearOfStudy" name="yearOfStudy">
                        <option value="">All Years</option>
                        <option value="1">First Year</option>
                        <option value="2">Second Year</option>
                        <option value="3">Third Year</option>
                        <option value="4">Fourth Year</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="registrationYear" class="form-label">Registration Year</label>
                    <select class="form-select" id="registrationYear" name="registrationYear">
                        <option value="">All Years</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="2020">2020</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <label for="searchMember" class="form-label">Search Member</label>
                    <input type="text" class="form-control" id="searchMember" placeholder="Enter name, ID, or email">
                </div>
                <div class="col-md-3">
                    <label for="activityLevel" class="form-label">Activity Level</label>
                    <select class="form-select" id="activityLevel" name="activityLevel">
                        <option value="">All Levels</option>
                        <option value="high">High Activity</option>
                        <option value="medium">Medium Activity</option>
                        <option value="low">Low Activity</option>
                        <option value="none">No Activity</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="fineStatus" class="form-label">Fine Status</label>
                    <select class="form-select" id="fineStatus" name="fineStatus">
                        <option value="">All</option>
                        <option value="clear">No Dues</option>
                        <option value="pending">Pending Fines</option>
                        <option value="overdue">Overdue Books</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-success" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-warning ms-2" onclick="sendNotifications()">
                        <i class="fas fa-bell me-2"></i>Send Notifications
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Member Statistics -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="totalMembers">0</h4>
                <p class="text-muted mb-0 small">Total Members</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-user-check fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="activeMembers">0</h4>
                <p class="text-muted mb-0 small">Active Members</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-user-clock fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="inactiveMembers">0</h4>
                <p class="text-muted mb-0 small">Inactive Members</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="newMembers">0</h4>
                <p class="text-muted mb-0 small">New This Month</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-user-times fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="suspendedMembers">0</h4>
                <p class="text-muted mb-0 small">Suspended</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="graduatedMembers">0</h4>
                <p class="text-muted mb-0 small">Graduated</p>
            </div>
        </div>
    </div>
</div>

<!-- Member Analysis Tabs -->
<ul class="nav nav-tabs mb-4" id="memberTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="member-list-tab" data-bs-toggle="tab" data-bs-target="#member-list" type="button" role="tab">
            <i class="fas fa-list me-2"></i>Member List
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="department-analysis-tab" data-bs-toggle="tab" data-bs-target="#department-analysis" type="button" role="tab">
            <i class="fas fa-chart-pie me-2"></i>Department Analysis
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="activity-analysis-tab" data-bs-toggle="tab" data-bs-target="#activity-analysis" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>Activity Analysis
        </button>
    </li>
</ul>

<div class="tab-content" id="memberTabContent">
    <!-- Member List Tab -->
    <div class="tab-pane fade show active" id="member-list" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Member Directory</h5>
                <div>
                    <span class="badge bg-success" id="activeCount">0 Active</span>
                    <span class="badge bg-warning ms-2" id="inactiveCount">0 Inactive</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="memberTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Member ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Year</th>
                                <th>Registration Date</th>
                                <th>Books Issued</th>
                                <th>Fine Status</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No members registered yet. Add students to see member data.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Department Analysis Tab -->
    <div class="tab-pane fade" id="department-analysis" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Department Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="departmentChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Department Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Department</th>
                                        <th>Total</th>
                                        <th>Active</th>
                                        <th>Activity Rate</th>
                                    </tr>
                                </thead>
                                <tbody id="departmentStatsTable">
                                    <tr>
                                        <td colspan="4" class="text-center text-muted">
                                            No department data available
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Analysis Tab -->
    <div class="tab-pane fade" id="activity-analysis" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Member Activity Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="activityChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Load initial data
    generateReport();
    
    // Initialize charts
    initializeDepartmentChart();
    initializeActivityChart();
});

function generateReport() {
    const filters = {
        memberStatus: $('#memberStatus').val(),
        department: $('#department').val(),
        yearOfStudy: $('#yearOfStudy').val(),
        registrationYear: $('#registrationYear').val(),
        searchMember: $('#searchMember').val(),
        activityLevel: $('#activityLevel').val(),
        fineStatus: $('#fineStatus').val()
    };
    
    // Show loading
    $('#memberTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading member data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadMemberData(filters);
    }, 1000);
}

function loadMemberData(filters) {
    // Since database is clean, show zero data
    const memberData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalMembers').text('0');
    $('#activeMembers').text('0');
    $('#inactiveMembers').text('0');
    $('#newMembers').text('0');
    $('#suspendedMembers').text('0');
    $('#graduatedMembers').text('0');
    $('#activeCount').text('0 Active');
    $('#inactiveCount').text('0 Inactive');
    
    // Update member table
    if (memberData.length === 0) {
        $('#memberTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-info">
                    <i class="fas fa-info-circle me-2"></i>No members registered yet. Add students to see member data.
                </td>
            </tr>
        `);
        
        $('#departmentStatsTable').html(`
            <tr>
                <td colspan="4" class="text-center text-muted">
                    No department data available
                </td>
            </tr>
        `);
    }
    
    // Update charts with zero data
    updateDepartmentChart([]);
    updateActivityChart([]);
}

function initializeDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    window.departmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['CSE', 'IT', 'ECE', 'EEE', 'MECH', 'CIVIL'],
            datasets: [{
                data: [0, 0, 0, 0, 0, 0],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function initializeActivityChart() {
    const ctx = document.getElementById('activityChart').getContext('2d');
    window.activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'New Registrations',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: 'Active Members',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateDepartmentChart(data) {
    if (window.departmentChart && data.length === 0) {
        window.departmentChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.departmentChart.update();
    }
}

function updateActivityChart(data) {
    if (window.activityChart && data.length === 0) {
        window.activityChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.activityChart.data.datasets[1].data = [0, 0, 0, 0, 0, 0];
        window.activityChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    generateReport();
}

function sendNotifications() {
    alert('Notification system would be implemented here.');
}

function exportReport(format) {
    alert(`Exporting Member Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
