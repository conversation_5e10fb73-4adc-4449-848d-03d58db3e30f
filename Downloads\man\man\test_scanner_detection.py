#!/usr/bin/env python3
"""
Test script for barcode scanner detection
This script tests the server-side scanner detection functionality
"""

import platform
import subprocess
import sys

def test_scanner_detection():
    """Test barcode scanner detection on the current system"""
    print("🔍 Testing Barcode Scanner Detection")
    print("=" * 50)
    
    system = platform.system()
    print(f"Operating System: {system}")
    
    scanner_detected = False
    scanner_info = []
    
    try:
        if system == "Windows":
            print("\n🪟 Checking Windows devices...")
            try:
                result = subprocess.run([
                    'wmic', 'path', 'Win32_PnPEntity', 'where', 
                    'DeviceID like "%HID%"', 'get', 'Name,DeviceID'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    output = result.stdout.lower()
                    print("HID Devices found:")
                    print(result.stdout)
                    
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra', 'datalogic']
                    
                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"Detected device containing '{keyword}'")
                            print(f"✅ Scanner keyword '{keyword}' found in device list")
                            break
                else:
                    print("❌ Failed to query Windows devices")
                    
            except Exception as e:
                print(f"❌ Windows device check failed: {e}")
                
        elif system == "Linux":
            print("\n🐧 Checking Linux USB devices...")
            try:
                result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    output = result.stdout.lower()
                    print("USB Devices:")
                    print(result.stdout)
                    
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra']
                    
                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"USB device containing '{keyword}' found")
                            print(f"✅ Scanner keyword '{keyword}' found in USB devices")
                            break
                else:
                    print("❌ Failed to query USB devices")
                    
            except Exception as e:
                print(f"❌ Linux device check failed: {e}")
                
        elif system == "Darwin":  # macOS
            print("\n🍎 Checking macOS USB devices...")
            try:
                result = subprocess.run([
                    'system_profiler', 'SPUSBDataType'
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    output = result.stdout.lower()
                    print("USB Devices found (showing first 1000 chars):")
                    print(result.stdout[:1000] + "..." if len(result.stdout) > 1000 else result.stdout)
                    
                    scanner_keywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra']
                    
                    for keyword in scanner_keywords:
                        if keyword in output:
                            scanner_detected = True
                            scanner_info.append(f"USB device containing '{keyword}' found")
                            print(f"✅ Scanner keyword '{keyword}' found in USB devices")
                            break
                else:
                    print("❌ Failed to query macOS USB devices")
                    
            except Exception as e:
                print(f"❌ macOS device check failed: {e}")
        
        print("\n" + "=" * 50)
        print("📊 DETECTION RESULTS")
        print("=" * 50)
        
        if scanner_detected:
            print("✅ BARCODE SCANNER DETECTED!")
            for info in scanner_info:
                print(f"   • {info}")
        else:
            print("❌ NO BARCODE SCANNER DETECTED")
            print("   • No scanner-related keywords found in device list")
            print("   • This could mean:")
            print("     - No barcode scanner is connected")
            print("     - Scanner is not recognized by the system")
            print("     - Scanner uses generic HID drivers without identifying keywords")
        
        print(f"\nSystem: {system}")
        print(f"Scanner Status: {'CONNECTED' if scanner_detected else 'NOT DETECTED'}")
        
        return scanner_detected, scanner_info
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        return False, [f"Error: {str(e)}"]

def test_common_scanner_vendors():
    """Test for common barcode scanner vendor IDs"""
    print("\n🏭 COMMON SCANNER VENDORS TO LOOK FOR:")
    print("=" * 50)
    
    vendors = {
        "0x05e0": "Symbol Technologies",
        "0x0c2e": "Honeywell",
        "0x1a86": "QinHeng Electronics (common for cheap scanners)",
        "0x04b4": "Cypress Semiconductor",
        "0x0536": "Hand Held Products (Honeywell)",
        "0x1eab": "Zebra Technologies",
        "0x0801": "Mag-Tek",
        "0x0483": "STMicroelectronics (some scanners)"
    }
    
    for vendor_id, name in vendors.items():
        print(f"   • {vendor_id}: {name}")
    
    print("\n💡 TIP: If you have a barcode scanner connected but it's not detected,")
    print("   check if it appears in your system's device manager with one of these vendor IDs.")

if __name__ == "__main__":
    print("🚀 Barcode Scanner Detection Test")
    print("This script will test if a barcode scanner is connected to your system.\n")
    
    detected, info = test_scanner_detection()
    test_common_scanner_vendors()
    
    print("\n" + "=" * 50)
    print("🔧 TROUBLESHOOTING TIPS")
    print("=" * 50)
    print("1. Ensure your barcode scanner is connected via USB")
    print("2. Check that the scanner is powered on (if it has a power button)")
    print("3. Try unplugging and reconnecting the scanner")
    print("4. Test the scanner in a text editor - it should type characters when you scan")
    print("5. Check your system's device manager for HID or USB devices")
    
    if not detected:
        print("\n⚠️  If you have a scanner connected but it's not detected:")
        print("   • The scanner might use generic HID drivers")
        print("   • It may still work with the web application")
        print("   • Try the 'Recheck Scanner' button in the gate dashboard")
    
    sys.exit(0 if detected else 1)
