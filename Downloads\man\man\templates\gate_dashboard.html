<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gate Entry Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .scanner-status {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #28a745;
        }

        .scanner-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .scanner-connected {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .main-table {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            overflow: hidden;
        }
        
        .stats-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
            border: none;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .stats-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .stats-card p {
            color: #6c757d;
            margin: 0;
            font-weight: 500;
        }

        .bg-primary-gradient {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .bg-success-gradient {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .bg-info-gradient {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .entry-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .entry-in {
            background: #d4edda;
            color: #155724;
        }

        .entry-out {
            background: #f8d7da;
            color: #721c24;
        }

        .scanner-hidden {
            position: absolute;
            left: -9999px;
            opacity: 0;
        }
        
        .logout-btn {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(220, 53, 69, 0.3);
            color: white;
        }

        .time-display {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }

        .status-online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .download-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .alert {
            border-radius: 0.5rem;
            border: none;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .disabled-state {
            opacity: 0.6;
            pointer-events: none;
        }

        .scanner-error .fa-exclamation-triangle {
            color: #dc3545;
        }

        .scanner-connected .fa-check-circle {
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">
                        <i class="fas fa-door-open me-2"></i>Gate Entry Dashboard
                        <span class="status-indicator" id="scannerStatus"></span>
                    </h4>
                    <small class="text-muted">Operator: {{ session.gate_username }}</small>
                </div>
                <div class="d-flex align-items-center">
                    <div class="time-display me-3" id="currentTime"></div>
                    <button class="btn download-btn me-2" onclick="downloadReport()">
                        <i class="fas fa-download me-2"></i>Download Report
                    </button>
                    <button class="btn btn-outline-secondary btn-sm me-2" onclick="recheckScanner()" title="Recheck Scanner">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <a href="{{ url_for('gate_logout') }}" class="btn logout-btn">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container-fluid py-4">
        <!-- Scanner Status -->
        <div class="scanner-status" id="scannerStatusCard">
            <div class="d-flex align-items-center">
                <i class="fas fa-qrcode fa-2x me-3"></i>
                <div>
                    <h5 class="mb-1" id="scannerStatusText">Checking barcode scanner...</h5>
                    <p class="mb-0 text-muted" id="scannerStatusDesc">Please wait while we detect your barcode scanner.</p>
                </div>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card bg-primary-gradient text-white">
                    <h3 id="todayEntries">{{ total_entries_today }}</h3>
                    <p><i class="fas fa-calendar-day me-2"></i>Today's Entries</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card bg-success-gradient text-white">
                    <h3 id="currentlyInside">{{ currently_inside }}</h3>
                    <p><i class="fas fa-users me-2"></i>Currently Inside</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card bg-info-gradient text-white">
                    <h3 id="liveTime">--:--</h3>
                    <p><i class="fas fa-clock me-2"></i>Current Time</p>
                </div>
            </div>
        </div>
        
        <!-- Hidden Scanner Input -->
        <input type="text" class="scanner-hidden" id="barcodeInput" autofocus>

        <!-- Main Entry Table -->
        <div class="main-table">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table me-2"></i>Today's Entry/Exit Log
                    </h5>
                    <div>
                        <button class="btn btn-sm btn-outline-light me-2" onclick="refreshTable()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <span class="badge bg-light text-dark" id="lastUpdate">Last updated: --:--</span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>User ID</th>
                                <th>Student Name</th>
                                <th>College</th>
                                <th>Department</th>
                                <th>In Time</th>
                                <th>Out Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="entryTableBody">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="empty-state">
                                        <i class="fas fa-qrcode"></i>
                                        <h6>Ready for Barcode Scanning</h6>
                                        <p class="mb-0">Scan a student barcode to begin tracking entries</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Alert for scan results -->
        <div class="alert alert-dismissible fade" id="scanAlert" style="display: none; position: fixed; top: 100px; right: 20px; z-index: 1050; min-width: 300px;">
            <span id="scanAlertText"></span>
            <button type="button" class="btn-close" onclick="hideScanAlert()"></button>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let scannerConnected = false;
        let entryData = [];

        $(document).ready(function() {
            updateTime();
            setInterval(updateTime, 1000);

            // Check scanner connection
            checkScannerConnection();

            // Auto-focus on hidden barcode input
            $('#barcodeInput').focus();

            // Handle barcode input
            $('#barcodeInput').on('input', function() {
                const value = $(this).val().trim();
                if (value.length >= 6) { // Assuming minimum barcode length
                    processBarcodeScan(value);
                    $(this).val(''); // Clear input
                }
            });

            // Keep focus on barcode input
            $(document).on('click', function() {
                if (scannerConnected) {
                    $('#barcodeInput').focus();
                }
            });

            // Load initial data
            loadTodayEntries();

            // Auto-refresh every 30 seconds
            setInterval(function() {
                loadTodayEntries();
                updateStatistics();
            }, 30000);
        });

        function checkScannerConnection() {
            // Check for barcode scanner using multiple methods
            detectBarcodeScanner().then(function(detected) {
                scannerConnected = detected;
                updateScannerStatus(detected);
            }).catch(function(error) {
                console.error('Scanner detection error:', error);
                scannerConnected = false;
                updateScannerStatus(false);
            });
        }

        async function detectBarcodeScanner() {
            try {
                // Method 1: Check for WebHID API support and HID devices
                if ('hid' in navigator) {
                    try {
                        const devices = await navigator.hid.getDevices();
                        const scannerDevices = devices.filter(device =>
                            isBarcodeScanner(device)
                        );

                        if (scannerDevices.length > 0) {
                            console.log('Barcode scanner detected via WebHID:', scannerDevices);
                            return true;
                        }
                    } catch (hidError) {
                        console.log('WebHID check failed:', hidError);
                    }
                }

                // Method 2: Check for USB devices via WebUSB (if available)
                if ('usb' in navigator) {
                    try {
                        const devices = await navigator.usb.getDevices();
                        const scannerDevices = devices.filter(device =>
                            isUSBBarcodeScanner(device)
                        );

                        if (scannerDevices.length > 0) {
                            console.log('Barcode scanner detected via WebUSB:', scannerDevices);
                            return true;
                        }
                    } catch (usbError) {
                        console.log('WebUSB check failed:', usbError);
                    }
                }

                // Method 3: Check server-side device detection
                const serverDetection = await checkServerSideScanner();
                if (serverDetection) {
                    return true;
                }

                // Method 4: Test for scanner input behavior (last resort)
                return await testScannerInput();

            } catch (error) {
                console.error('Scanner detection failed:', error);
                return false;
            }
        }

        function isBarcodeScanner(device) {
            // Common barcode scanner vendor IDs and product IDs
            const scannerVendorIds = [
                0x05e0, // Symbol Technologies
                0x0c2e, // Honeywell
                0x1a86, // QinHeng Electronics (common for cheap scanners)
                0x04b4, // Cypress Semiconductor
                0x0536, // Hand Held Products (Honeywell)
                0x1eab, // Zebra Technologies
                0x0801, // Mag-Tek
                0x1234, // Generic scanner ID
                0x0483  // STMicroelectronics (some scanners)
            ];

            // Check if device is a HID keyboard-like device (most barcode scanners)
            const isHIDKeyboard = device.collections && device.collections.some(collection =>
                collection.usage === 0x06 && collection.usagePage === 0x01 // Generic Desktop, Keyboard
            );

            // Check vendor ID
            const isKnownScanner = scannerVendorIds.includes(device.vendorId);

            // Check product name for scanner keywords
            const productName = (device.productName || '').toLowerCase();
            const scannerKeywords = ['scanner', 'barcode', 'symbol', 'honeywell', 'zebra', 'datalogic'];
            const hasKeyword = scannerKeywords.some(keyword => productName.includes(keyword));

            return isKnownScanner || hasKeyword || (isHIDKeyboard && productName.includes('scanner'));
        }

        function isUSBBarcodeScanner(device) {
            // USB class codes for HID devices
            const isHIDDevice = device.configuration &&
                device.configuration.interfaces.some(interface =>
                    interface.interfaceClass === 3 // HID class
                );

            // Check vendor/product IDs
            const scannerVendorIds = [0x05e0, 0x0c2e, 0x1a86, 0x04b4, 0x0536, 0x1eab];
            const isKnownScanner = scannerVendorIds.includes(device.vendorId);

            return isHIDDevice && isKnownScanner;
        }

        async function checkServerSideScanner() {
            try {
                const response = await fetch('/api/check-scanner');
                const data = await response.json();

                if (data.scanner_detected) {
                    console.log('Server-side scanner detection:', data);
                    return true;
                }

                return false;
            } catch (error) {
                console.error('Server-side scanner check failed:', error);
                return false;
            }
        }

        async function testScannerInput() {
            return new Promise((resolve) => {
                // Test for rapid keyboard input characteristic of barcode scanners
                let inputBuffer = '';
                let inputStartTime = 0;
                let rapidInputDetected = false;

                const testInput = (event) => {
                    const currentTime = Date.now();

                    if (inputBuffer.length === 0) {
                        inputStartTime = currentTime;
                    }

                    inputBuffer += event.key;

                    // Check for rapid input (typical of barcode scanners)
                    const inputDuration = currentTime - inputStartTime;
                    const inputSpeed = inputBuffer.length / (inputDuration / 1000); // chars per second

                    // Barcode scanners typically input at 100+ characters per second
                    if (inputSpeed > 50 && inputBuffer.length > 5) {
                        rapidInputDetected = true;
                        document.removeEventListener('keydown', testInput);
                        resolve(true);
                    }

                    // Reset buffer after 1 second of no input
                    setTimeout(() => {
                        if (Date.now() - currentTime > 1000) {
                            inputBuffer = '';
                        }
                    }, 1000);
                };

                // Listen for keyboard input for 5 seconds
                document.addEventListener('keydown', testInput);

                setTimeout(() => {
                    document.removeEventListener('keydown', testInput);
                    if (!rapidInputDetected) {
                        resolve(false);
                    }
                }, 5000);
            });
        }

        function updateScannerStatus(connected) {
            const statusCard = $('#scannerStatusCard');
            const statusIndicator = $('#scannerStatus');
            const statusText = $('#scannerStatusText');
            const statusDesc = $('#scannerStatusDesc');

            if (connected) {
                statusCard.removeClass('scanner-error').addClass('scanner-connected');
                statusIndicator.removeClass('status-offline').addClass('status-online');
                statusText.html('<i class="fas fa-check-circle me-2"></i>Barcode Scanner Connected');
                statusDesc.text('Hardware scanner detected and ready. Scan student ID barcodes to track entry/exit.');
                $('#barcodeInput').focus();

                // Enable scanning functionality
                enableScanning();
            } else {
                statusCard.removeClass('scanner-connected').addClass('scanner-error');
                statusIndicator.removeClass('status-online').addClass('status-offline');
                statusText.html('<i class="fas fa-exclamation-triangle me-2"></i>No Barcode Scanner Detected');
                statusDesc.html('Please connect a USB barcode scanner to use this system.<br><strong>Manual entry is disabled for security.</strong>');

                // Disable scanning functionality
                disableScanning();
            }
        }

        function enableScanning() {
            // Enable the hidden input and scanning functions
            $('#barcodeInput').prop('disabled', false);
            $('.main-table').removeClass('disabled-state');

            // Show instructions
            if ($('#entryTableBody tr').length === 1 && $('#entryTableBody .empty-state').length > 0) {
                $('#entryTableBody .empty-state h6').text('Ready for Barcode Scanning');
                $('#entryTableBody .empty-state p').text('Scan a student barcode to begin tracking entries');
                $('#entryTableBody .empty-state i').removeClass('fa-exclamation-triangle').addClass('fa-qrcode');
            }
        }

        function disableScanning() {
            // Disable the hidden input and scanning functions
            $('#barcodeInput').prop('disabled', true);
            $('.main-table').addClass('disabled-state');

            // Show error message in table
            if ($('#entryTableBody tr').length === 1 && $('#entryTableBody .empty-state').length > 0) {
                $('#entryTableBody .empty-state h6').text('Scanner Required');
                $('#entryTableBody .empty-state p').text('Connect a barcode scanner to enable entry tracking');
                $('#entryTableBody .empty-state i').removeClass('fa-qrcode').addClass('fa-exclamation-triangle');
            }
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const dateString = now.toLocaleDateString();

            $('#currentTime').text(dateString + ' ' + timeString);
            $('#liveTime').text(timeString);
            $('#lastUpdate').text('Last updated: ' + timeString);
        }

        function processBarcodeScan(userId) {
            if (!scannerConnected) {
                showScanAlert('Barcode scanner not detected! Please connect a scanner.', 'danger');
                return;
            }

            if ($('#barcodeInput').prop('disabled')) {
                showScanAlert('Scanning is disabled. Check scanner connection.', 'warning');
                return;
            }

            showScanAlert('Processing scan...', 'info');

            $.ajax({
                url: '/gate-scan',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ user_id: userId }),
                success: function(response) {
                    if (response.success) {
                        showScanAlert(
                            `${response.entry_type}: ${response.student_name} (${response.user_id})`,
                            'success'
                        );

                        // Update table
                        updateEntryTable(response);
                        updateStatistics();

                    } else {
                        showScanAlert(response.message, 'warning');
                    }
                },
                error: function(xhr) {
                    const response = JSON.parse(xhr.responseText);
                    showScanAlert('Error: ' + response.error, 'danger');
                }
            });
        }

        function updateEntryTable(scanData) {
            const tbody = $('#entryTableBody');

            // Check if this user already has a row
            let existingRow = tbody.find(`tr[data-user-id="${scanData.user_id}"]`);

            if (existingRow.length > 0 && scanData.entry_type === 'OUT') {
                // Update existing row with exit time
                existingRow.find('.out-time').text(new Date(scanData.time).toLocaleTimeString());
                existingRow.find('.status-badge').removeClass('entry-in').addClass('entry-out').text('OUT');
            } else if (scanData.entry_type === 'IN') {
                // Add new row for entry
                const newRow = `
                    <tr data-user-id="${scanData.user_id}">
                        <td><strong>${scanData.user_id}</strong></td>
                        <td>${scanData.student_name}</td>
                        <td>${scanData.college}</td>
                        <td>${scanData.department}</td>
                        <td>${new Date(scanData.time).toLocaleTimeString()}</td>
                        <td class="out-time">-</td>
                        <td><span class="entry-badge entry-in status-badge">IN</span></td>
                    </tr>
                `;

                // Remove empty state if present
                tbody.find('.empty-state').closest('tr').remove();

                // Add new row at the top
                tbody.prepend(newRow);
            }
        }

        function loadTodayEntries() {
            const today = new Date().toISOString().split('T')[0];

            $.ajax({
                url: `/api/admin/gate-entries?start_date=${today}&end_date=${today}`,
                method: 'GET',
                success: function(response) {
                    populateTable(response.entries);
                },
                error: function() {
                    console.error('Failed to load today\'s entries');
                }
            });
        }

        function populateTable(entries) {
            const tbody = $('#entryTableBody');
            tbody.empty();

            if (entries.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="empty-state">
                                <i class="fas fa-qrcode"></i>
                                <h6>Ready for Barcode Scanning</h6>
                                <p class="mb-0">Scan a student barcode to begin tracking entries</p>
                            </div>
                        </td>
                    </tr>
                `);
                return;
            }

            // Group entries by user_id to show in/out pairs
            const userEntries = {};
            entries.forEach(entry => {
                if (!userEntries[entry.user_id]) {
                    userEntries[entry.user_id] = {
                        user_id: entry.user_id,
                        student_name: entry.student_name,
                        college_name: entry.college_name,
                        department_name: entry.department_name,
                        in_time: null,
                        out_time: null,
                        status: 'OUT'
                    };
                }

                if (entry.entry_type === 'IN') {
                    userEntries[entry.user_id].in_time = entry.entry_time;
                    userEntries[entry.user_id].status = entry.exit_time === 'Still Inside' ? 'IN' : 'OUT';
                }
                if (entry.exit_time !== 'Still Inside') {
                    userEntries[entry.user_id].out_time = entry.exit_time;
                }
            });

            // Populate table
            Object.values(userEntries).forEach(user => {
                const statusBadge = user.status === 'IN' ?
                    '<span class="entry-badge entry-in">IN</span>' :
                    '<span class="entry-badge entry-out">OUT</span>';

                tbody.append(`
                    <tr data-user-id="${user.user_id}">
                        <td><strong>${user.user_id}</strong></td>
                        <td>${user.student_name}</td>
                        <td>${user.college_name}</td>
                        <td>${user.department_name}</td>
                        <td>${user.in_time ? new Date(user.in_time).toLocaleTimeString() : '-'}</td>
                        <td class="out-time">${user.out_time ? new Date(user.out_time).toLocaleTimeString() : '-'}</td>
                        <td>${statusBadge}</td>
                    </tr>
                `);
            });
        }

        function updateStatistics() {
            const today = new Date().toISOString().split('T')[0];

            $.ajax({
                url: `/api/admin/gate-entries?start_date=${today}&end_date=${today}`,
                method: 'GET',
                success: function(response) {
                    $('#todayEntries').text(response.total_count);

                    const currentlyInside = response.entries.filter(entry =>
                        entry.exit_time === 'Still Inside'
                    ).length;
                    $('#currentlyInside').text(currentlyInside);
                }
            });
        }

        function showScanAlert(message, type) {
            const alert = $('#scanAlert');
            const alertText = $('#scanAlertText');

            alert.removeClass('alert-success alert-danger alert-warning alert-info')
                 .addClass(`alert-${type}`)
                 .addClass('show')
                 .show();

            alertText.html(message);

            // Auto-hide after 3 seconds
            setTimeout(hideScanAlert, 3000);
        }

        function hideScanAlert() {
            $('#scanAlert').removeClass('show').fadeOut();
        }

        function refreshTable() {
            loadTodayEntries();
            updateStatistics();
            showScanAlert('Table refreshed', 'info');
        }

        function downloadReport() {
            const today = new Date().toISOString().split('T')[0];

            // Create download form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/api/admin/gate-entries/export';
            form.style.display = 'none';

            const startDateInput = document.createElement('input');
            startDateInput.name = 'start_date';
            startDateInput.value = today;
            form.appendChild(startDateInput);

            const endDateInput = document.createElement('input');
            endDateInput.name = 'end_date';
            endDateInput.value = today;
            form.appendChild(endDateInput);

            const formatInput = document.createElement('input');
            formatInput.name = 'format';
            formatInput.value = 'excel';
            form.appendChild(formatInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);

            showScanAlert('Report download started', 'success');
        }

        function recheckScanner() {
            showScanAlert('Rechecking scanner connection...', 'info');
            $('#scannerStatusText').text('Rechecking barcode scanner...');
            $('#scannerStatusDesc').text('Please wait while we detect your barcode scanner.');

            // Reset status
            scannerConnected = false;
            updateScannerStatus(false);

            // Recheck after a short delay
            setTimeout(() => {
                checkScannerConnection();
            }, 1000);
        }
    </script>
</body>
</html>
