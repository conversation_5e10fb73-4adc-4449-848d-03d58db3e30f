{% extends "base.html" %}

{% block title %}Bibliography Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-credit-card me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-book-open me-3 text-primary"></i>Bibliography Report</h2>
        <p class="text-muted mb-0">Generate comprehensive bibliography and collection reports</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category">
                    <option value="">All Categories</option>
                    <option value="fiction">Fiction</option>
                    <option value="non-fiction">Non-Fiction</option>
                    <option value="reference">Reference</option>
                    <option value="textbook">Textbook</option>
                    <option value="journal">Journal</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="subject" class="form-label">Subject</label>
                <select class="form-select" id="subject">
                    <option value="">All Subjects</option>
                    <option value="computer-science">Computer Science</option>
                    <option value="mathematics">Mathematics</option>
                    <option value="physics">Physics</option>
                    <option value="chemistry">Chemistry</option>
                    <option value="biology">Biology</option>
                    <option value="literature">Literature</option>
                    <option value="history">History</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="language" class="form-label">Language</label>
                <select class="form-select" id="language">
                    <option value="">All Languages</option>
                    <option value="english">English</option>
                    <option value="hindi">Hindi</option>
                    <option value="marathi">Marathi</option>
                    <option value="gujarati">Gujarati</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="yearRange" class="form-label">Publication Year</label>
                <select class="form-select" id="yearRange">
                    <option value="">All Years</option>
                    <option value="2020-2024">2020-2024</option>
                    <option value="2015-2019">2015-2019</option>
                    <option value="2010-2014">2010-2014</option>
                    <option value="2000-2009">2000-2009</option>
                    <option value="before-2000">Before 2000</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <label for="author" class="form-label">Author</label>
                <input type="text" class="form-control" id="author" placeholder="Search by author name">
            </div>
            <div class="col-md-6">
                <label for="publisher" class="form-label">Publisher</label>
                <input type="text" class="form-control" id="publisher" placeholder="Search by publisher">
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Generate Report
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Books</h6>
                        <h3 class="mb-0" id="totalBooks">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Unique Authors</h6>
                        <h3 class="mb-0" id="uniqueAuthors">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Publishers</h6>
                        <h3 class="mb-0" id="totalPublishers">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Subjects</h6>
                        <h3 class="mb-0" id="totalSubjects">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bibliography Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Bibliography</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="bibliographyTable">
                <thead class="table-dark">
                    <tr>
                        <th>Title</th>
                        <th>Author(s)</th>
                        <th>Publisher</th>
                        <th>Year</th>
                        <th>ISBN</th>
                        <th>Category</th>
                        <th>Subject</th>
                        <th>Language</th>
                        <th>Copies</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="bibliographyTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    // Initialize empty table
    $('#bibliographyTableBody').html('<tr><td colspan="10" class="text-center">Click "Generate Report" to load bibliography data</td></tr>');
    updateStatistics({});
});

function loadBibliographyData() {
    // Show loading state
    $('#bibliographyTableBody').html('<tr><td colspan="10" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');

    // Get filter values
    const filters = {
        category: $('#category').val(),
        subject: $('#subject').val(),
        language: $('#language').val(),
        yearRange: $('#yearRange').val(),
        author: $('#author').val(),
        publisher: $('#publisher').val()
    };

    // TODO: Replace with actual API call
    // Example: fetch('/api/librarian/bibliography-data', { method: 'POST', body: JSON.stringify(filters) })
    //   .then(response => response.json())
    //   .then(data => {
    //       updateStatistics(data.statistics);
    //       populateTable(data.books);
    //   });

    // For now, show empty state
    setTimeout(function() {
        $('#bibliographyTableBody').html('<tr><td colspan="10" class="text-center">No data available. Please implement API endpoint.</td></tr>');
        updateStatistics({});
    }, 500);
}

function updateStatistics(stats) {
    $('#totalBooks').text(stats.totalBooks || 0);
    $('#uniqueAuthors').text(stats.uniqueAuthors || 0);
    $('#totalPublishers').text(stats.totalPublishers || 0);
    $('#totalSubjects').text(stats.totalSubjects || 0);
}

function populateTable(data) {
    const tbody = $('#bibliographyTableBody');
    tbody.empty();

    if (!data || data.length === 0) {
        tbody.html('<tr><td colspan="10" class="text-center">No bibliography records found</td></tr>');
        return;
    }

    data.forEach(function(book) {
        const row = `
            <tr>
                <td><strong>${book.title || '-'}</strong></td>
                <td>${book.authors || '-'}</td>
                <td>${book.publisher || '-'}</td>
                <td>${book.year || '-'}</td>
                <td>${book.isbn || '-'}</td>
                <td><span class="badge bg-${getCategoryColor(book.category)}">${book.category || 'Unknown'}</span></td>
                <td>${book.subject || '-'}</td>
                <td>${book.language || '-'}</td>
                <td>${book.copies || 0}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${book.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getCategoryColor(category) {
    const colors = {
        'fiction': 'primary',
        'non-fiction': 'success',
        'reference': 'info',
        'textbook': 'warning',
        'journal': 'secondary'
    };
    return colors[category] || 'secondary';
}

function applyFilters() {
    loadBibliographyData();
}

function resetFilters() {
    $('#category').val('');
    $('#subject').val('');
    $('#language').val('');
    $('#yearRange').val('');
    $('#author').val('');
    $('#publisher').val('');
}

function exportReport(format) {
    // TODO: Implement actual export functionality
    const filters = {
        category: $('#category').val(),
        subject: $('#subject').val(),
        language: $('#language').val(),
        yearRange: $('#yearRange').val(),
        author: $('#author').val(),
        publisher: $('#publisher').val(),
        format: format
    };

    console.log('Export request:', filters);
    alert('Export functionality needs to be implemented');
}

function viewDetails(bookId) {
    // TODO: Implement view details functionality
    console.log('View details for book:', bookId);
    alert('View details functionality needs to be implemented');
}
</script>
{% endblock %}
