{% extends "base.html" %}

{% block title %}Manage Students - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-graduation-cap me-3"></i>Manage Students</h2>
    <div>
        <a href="{{ url_for('admin_bulk_users') }}" class="btn btn-info btn-custom me-2">
            <i class="fas fa-upload me-2"></i>Bulk Create Users
        </a>
        <a href="{{ url_for('admin_add_student') }}" class="btn btn-primary btn-custom">
            <i class="fas fa-plus me-2"></i>Add New Student
        </a>
    </div>
</div>

<!-- Search Bar -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="searchInput" placeholder="Search by name, user ID, roll number, or department..." onkeyup="searchStudents()">
            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    <div class="col-md-6">
        <div class="d-flex justify-content-end">
            <select class="form-select" id="departmentFilter" onchange="filterByDepartment()" style="max-width: 200px;">
                <option value="">All Departments</option>
                <option value="CSE">CSE</option>
                <option value="IT">IT</option>
                <option value="ECE">ECE</option>
                <option value="EEE">EEE</option>
                <option value="BME">BME</option>
                <option value="AERO">AERO</option>
                <option value="AIDS">AIDS</option>
                <option value="CSBS">CSBS</option>
                <option value="CIVIL">CIVIL</option>
                <option value="PCT">PCT</option>
            </select>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Student Accounts</h5>
    </div>
    <div class="card-body">
        {% if students %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Roll Number</th>
                        <th>Department</th>
                        <th>Designation</th>
                        <th>Validity Date</th>
                        <th>Active Issues</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="studentsTableBody">
                    {% for student in students %}
                    <tr class="student-row"
                        data-name="{{ student.name|lower }}"
                        data-roll="{{ student.roll_number|lower }}"
                        data-email="{{ student.email|lower }}"
                        data-department="{{ (student.department or '')|lower }}">
                        <td><strong>{{ student.user_id or student.id }}</strong></td>
                        <td>{{ student.name }}</td>
                        <td><code>{{ student.email }}</code></td>
                        <td>{{ student.roll_number }}</td>
                        <td>
                            <span class="badge bg-info">{{ student.department or 'N/A' }}</span>
                        </td>
                        <td>
                            <span class="badge {% if student.designation == 'Staff' %}bg-warning{% else %}bg-primary{% endif %}">
                                {{ student.designation or 'Student' }}
                            </span>
                        </td>
                        <td>
                            {% if student.validity_date %}
                                {{ student.validity_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set active_issues = student.issues|selectattr('return_date', 'none')|list|length %}
                            <span class="badge {% if active_issues > 0 %}bg-warning{% else %}bg-success{% endif %}">
                                {{ active_issues }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-success">Active</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-info me-1" 
                                    onclick="viewStudentDetails({{ student.id }})" 
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" 
                                    onclick="deleteStudent({{ student.id }}, '{{ student.name }}')" 
                                    title="Delete Student">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No students found</h5>
            <p class="text-muted">Start by adding your first student account.</p>
            <a href="{{ url_for('admin_add_student') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add First Student
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Student Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card stat-card stat-card-primary">
            <div class="card-body text-center">
                <i class="fas fa-graduation-cap fa-2x text-primary mb-2"></i>
                <h4>{{ students|length }}</h4>
                <p class="text-muted mb-0">Total Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-success">
            <div class="card-body text-center">
                <i class="fas fa-book-open fa-2x text-success mb-2"></i>
                <h4>
                    {% set active_issues = [] %}
                    {% for student in students %}
                        {% for issue in student.issues %}
                            {% if issue.return_date is none %}
                                {% set _ = active_issues.append(issue) %}
                            {% endif %}
                        {% endfor %}
                    {% endfor %}
                    {{ active_issues|length }}
                </h4>
                <p class="text-muted mb-0">Active Issues</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stat-card stat-card-info">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x text-info mb-2"></i>
                <h4>{{ students|length }}</h4>
                <p class="text-muted mb-0">Active Students</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function searchStudents() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('.student-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const name = row.getAttribute('data-name');
        const roll = row.getAttribute('data-roll');
        const email = row.getAttribute('data-email');
        const department = row.getAttribute('data-department');

        if (name.includes(searchTerm) ||
            roll.includes(searchTerm) ||
            email.includes(searchTerm) ||
            department.includes(searchTerm)) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    updateSearchResults(visibleCount);
}

function filterByDepartment() {
    const selectedDept = document.getElementById('departmentFilter').value.toLowerCase();
    const rows = document.querySelectorAll('.student-row');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const department = row.getAttribute('data-department');
        
        if (selectedDept === '' || department === selectedDept) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // Clear search input when filtering by department
    document.getElementById('searchInput').value = '';
    updateSearchResults(visibleCount);
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('departmentFilter').value = '';
    const rows = document.querySelectorAll('.student-row');
    rows.forEach(row => {
        row.style.display = '';
    });
    updateSearchResults(rows.length);
}

function updateSearchResults(count) {
    // You can add a results counter here if needed
    console.log(`Showing ${count} students`);
}

function deleteStudent(id, name) {
    if (confirm(`Are you sure you want to delete student "${name}"? This action cannot be undone.`)) {
        // Create a form and submit it
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/delete_student/' + id;
        document.body.appendChild(form);
        form.submit();
    }
}

function viewStudentDetails(id) {
    // Redirect to student details page
    window.location.href = `/admin/student_details/${id}`;
}
</script>
{% endblock %}
