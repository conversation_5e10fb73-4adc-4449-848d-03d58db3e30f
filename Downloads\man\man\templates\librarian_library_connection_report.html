{% extends "base.html" %}

{% block title %}Library Connection Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-credit-card me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1 active" href="{{ url_for('librarian_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- Tools & Utilities -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#toolsSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-tools me-2"></i>Tools
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="toolsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_barcode_generator') }}">
                <i class="fas fa-barcode me-2"></i>Barcode Generator
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_label_printer') }}">
                <i class="fas fa-print me-2"></i>Label Printer
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_inventory_check') }}">
                <i class="fas fa-clipboard-check me-2"></i>Inventory Check
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_notifications') }}">
                <i class="fas fa-bell me-2"></i>Notifications
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-wifi me-3 text-primary"></i>Library Connection Report</h2>
        <p class="text-muted mb-0">Monitor library system connections and network usage</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="connectionType" class="form-label">Connection Type</label>
                <select class="form-select" id="connectionType">
                    <option value="">All Types</option>
                    <option value="wifi">WiFi</option>
                    <option value="ethernet">Ethernet</option>
                    <option value="mobile">Mobile</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="userType" class="form-label">User Type</label>
                <select class="form-select" id="userType">
                    <option value="">All Users</option>
                    <option value="student">Students</option>
                    <option value="faculty">Faculty</option>
                    <option value="staff">Staff</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Connections</h6>
                        <h3 class="mb-0" id="activeConnections">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-wifi fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Sessions</h6>
                        <h3 class="mb-0" id="totalSessions">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Avg Duration</h6>
                        <h3 class="mb-0" id="avgDuration">0h 0m</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Peak Hour</h6>
                        <h3 class="mb-0" id="peakHour">--:--</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Connection Data Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Connection Sessions</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="connectionTable">
                <thead class="table-dark">
                    <tr>
                        <th>User ID</th>
                        <th>Name</th>
                        <th>Connection Type</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Duration</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="connectionTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadConnectionData();
    
    // Set default dates (last 7 days)
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastWeek.toISOString().split('T')[0]);
});

function loadConnectionData() {
    // Show loading state
    $('#connectionTableBody').html('<tr><td colspan="8" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            statistics: {
                activeConnections: 25,
                totalSessions: 150,
                avgDuration: '2h 30m',
                peakHour: '10:30 AM'
            },
            data: [
                {
                    id: '1',
                    userId: 'STU001',
                    name: 'John Doe',
                    connectionType: 'wifi',
                    startTime: '2024-01-15 09:30:00',
                    endTime: '2024-01-15 11:45:00',
                    duration: '2h 15m',
                    status: 'Disconnected'
                },
                {
                    id: '2',
                    userId: 'STU002',
                    name: 'Jane Smith',
                    connectionType: 'ethernet',
                    startTime: '2024-01-15 10:00:00',
                    endTime: null,
                    duration: '1h 30m',
                    status: 'Connected'
                }
            ]
        };
        
        updateStatistics(mockData.statistics);
        populateTable(mockData.data);
    }, 1000);
}

function updateStatistics(stats) {
    $('#activeConnections').text(stats.activeConnections || 0);
    $('#totalSessions').text(stats.totalSessions || 0);
    $('#avgDuration').text(stats.avgDuration || '0h 0m');
    $('#peakHour').text(stats.peakHour || '--:--');
}

function populateTable(data) {
    const tbody = $('#connectionTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="8" class="text-center">No connection data found</td></tr>');
        return;
    }
    
    data.forEach(function(connection) {
        const row = `
            <tr>
                <td>${connection.userId}</td>
                <td>${connection.name}</td>
                <td><span class="badge bg-${getConnectionTypeColor(connection.connectionType)}">${connection.connectionType}</span></td>
                <td>${connection.startTime}</td>
                <td>${connection.endTime || '-'}</td>
                <td>${connection.duration}</td>
                <td><span class="badge bg-${getStatusColor(connection.status)}">${connection.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${connection.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getConnectionTypeColor(type) {
    const colors = {
        'wifi': 'primary',
        'ethernet': 'success',
        'mobile': 'warning'
    };
    return colors[type] || 'secondary';
}

function getStatusColor(status) {
    return status === 'Connected' ? 'success' : 'secondary';
}

function applyFilters() {
    loadConnectionData();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#connectionType').val('');
    $('#userType').val('');
    loadConnectionData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewDetails(connectionId) {
    alert('View details for connection: ' + connectionId);
}
</script>
{% endblock %}
