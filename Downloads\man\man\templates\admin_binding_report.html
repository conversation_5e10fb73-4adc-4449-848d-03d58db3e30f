{% extends "base.html" %}

{% block title %}Binding Report - Admin{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Fixed Circulation Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
        </div>
    </div>
</div>

<!-- Fixed Reports Menu -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-tools me-3 text-warning"></i>Binding & Maintenance Report</h2>
        <p class="text-muted mb-0">Books in binding, vendor performance, maintenance costs, and repair tracking</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Binding Overview Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-tools fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalInBinding">0</h4>
                <p class="text-muted mb-0 small">In Binding</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="completedBinding">0</h4>
                <p class="text-muted mb-0 small">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                </div>
                <h4 class="text-danger" id="overdueBinding">0</h4>
                <p class="text-muted mb-0 small">Overdue</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-wrench fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="underRepair">0</h4>
                <p class="text-muted mb-0 small">Under Repair</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-building fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="activeVendors">0</h4>
                <p class="text-muted mb-0 small">Active Vendors</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-dark">
                    <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                </div>
                <h4 class="text-dark" id="totalCost">₹0</h4>
                <p class="text-muted mb-0 small">Total Cost</p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="card autolib-card mb-4">
    <div class="card-header bg-gradient-warning text-white">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Binding & Maintenance Filters</h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row">
                <div class="col-md-3">
                    <label for="serviceType" class="form-label">Service Type</label>
                    <select class="form-select" id="serviceType" name="serviceType">
                        <option value="">All Services</option>
                        <option value="binding">Book Binding</option>
                        <option value="repair">Book Repair</option>
                        <option value="rebinding">Re-binding</option>
                        <option value="restoration">Restoration</option>
                        <option value="cleaning">Cleaning</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="overdue">Overdue</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="vendor" class="form-label">Vendor</label>
                    <select class="form-select" id="vendor" name="vendor">
                        <option value="">All Vendors</option>
                        <option value="vendor1">ABC Binding Services</option>
                        <option value="vendor2">XYZ Book Repair</option>
                        <option value="vendor3">Quality Binding Co.</option>
                        <option value="internal">Internal Team</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priority</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <label for="dateFrom" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                </div>
                <div class="col-md-3">
                    <label for="dateTo" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                </div>
                <div class="col-md-3">
                    <label for="costRange" class="form-label">Cost Range</label>
                    <select class="form-select" id="costRange" name="costRange">
                        <option value="">All Costs</option>
                        <option value="0-500">₹0 - ₹500</option>
                        <option value="500-1000">₹500 - ₹1000</option>
                        <option value="1000-2000">₹1000 - ₹2000</option>
                        <option value="2000+">₹2000+</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="searchBook" class="form-label">Search Book</label>
                    <input type="text" class="form-control" id="searchBook" placeholder="Title, author, or access number">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="button" class="btn btn-warning" onclick="generateReport()">
                        <i class="fas fa-search me-2"></i>Generate Report
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                        <i class="fas fa-refresh me-2"></i>Reset
                    </button>
                    <button type="button" class="btn btn-primary ms-2" onclick="newBindingRequest()">
                        <i class="fas fa-plus me-2"></i>New Request
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Binding Tabs -->
<ul class="nav nav-tabs mb-4" id="bindingTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="active-binding-tab" data-bs-toggle="tab" data-bs-target="#active-binding" type="button" role="tab">
            <i class="fas fa-clock me-2"></i>Active Jobs
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="vendor-performance-tab" data-bs-toggle="tab" data-bs-target="#vendor-performance" type="button" role="tab">
            <i class="fas fa-building me-2"></i>Vendor Performance
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="cost-analysis-tab" data-bs-toggle="tab" data-bs-target="#cost-analysis" type="button" role="tab">
            <i class="fas fa-chart-bar me-2"></i>Cost Analysis
        </button>
    </li>
</ul>

<div class="tab-content" id="bindingTabContent">
    <!-- Active Jobs Tab -->
    <div class="tab-pane fade show active" id="active-binding" role="tabpanel">
        <div class="card autolib-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Active Binding & Maintenance Jobs</h5>
                <div>
                    <span class="badge bg-warning" id="pendingJobs">0 Pending</span>
                    <span class="badge bg-info ms-2" id="inProgressJobs">0 In Progress</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="activeJobsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>Job ID</th>
                                <th>Book Details</th>
                                <th>Service Type</th>
                                <th>Vendor</th>
                                <th>Start Date</th>
                                <th>Expected Completion</th>
                                <th>Cost (₹)</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center text-success">
                                    <i class="fas fa-check-circle me-2"></i>No active binding jobs. All books are in good condition!
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Vendor Performance Tab -->
    <div class="tab-pane fade" id="vendor-performance" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Vendor Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="vendorChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Vendor Performance Metrics</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Vendor</th>
                                        <th>Jobs Completed</th>
                                        <th>Avg. Time (Days)</th>
                                        <th>Quality Rating</th>
                                        <th>On-Time %</th>
                                    </tr>
                                </thead>
                                <tbody id="vendorPerformanceTable">
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">
                                            No vendor performance data available
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Cost Analysis Tab -->
    <div class="tab-pane fade" id="cost-analysis" role="tabpanel">
        <div class="row">
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Monthly Cost Trends</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="costTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Service Type Costs</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="serviceCostChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card autolib-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>Cost Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary" id="totalSpent">₹0</h4>
                                    <p class="text-muted">Total Spent</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success" id="avgJobCost">₹0</h4>
                                    <p class="text-muted">Average Job Cost</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning" id="monthlyBudget">₹0</h4>
                                    <p class="text-muted">Monthly Budget</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info" id="budgetUtilization">0%</h4>
                                    <p class="text-muted">Budget Utilization</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Set default dates (last 30 days)
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    // Load initial data
    generateReport();
    
    // Initialize charts
    initializeBindingCharts();
});

function generateReport() {
    const filters = {
        serviceType: $('#serviceType').val(),
        status: $('#status').val(),
        vendor: $('#vendor').val(),
        priority: $('#priority').val(),
        dateFrom: $('#dateFrom').val(),
        dateTo: $('#dateTo').val(),
        costRange: $('#costRange').val(),
        searchBook: $('#searchBook').val()
    };
    
    // Show loading
    $('#activeJobsTable tbody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>Loading binding data...</td></tr>');
    
    // Simulate API call - replace with actual implementation
    setTimeout(() => {
        loadBindingData(filters);
    }, 1000);
}

function loadBindingData(filters) {
    // Since database is clean, show zero data
    const bindingData = []; // Empty array for clean database
    
    // Update statistics - all zeros for clean database
    $('#totalInBinding').text('0');
    $('#completedBinding').text('0');
    $('#overdueBinding').text('0');
    $('#underRepair').text('0');
    $('#activeVendors').text('0');
    $('#totalCost').text('₹0');
    $('#pendingJobs').text('0 Pending');
    $('#inProgressJobs').text('0 In Progress');
    
    // Update cost metrics
    $('#totalSpent').text('₹0');
    $('#avgJobCost').text('₹0');
    $('#monthlyBudget').text('₹0');
    $('#budgetUtilization').text('0%');
    
    // Update active jobs table
    if (bindingData.length === 0) {
        $('#activeJobsTable tbody').html(`
            <tr>
                <td colspan="9" class="text-center text-success">
                    <i class="fas fa-check-circle me-2"></i>No active binding jobs. All books are in good condition!
                </td>
            </tr>
        `);
        
        $('#vendorPerformanceTable').html(`
            <tr>
                <td colspan="5" class="text-center text-muted">
                    No vendor performance data available
                </td>
            </tr>
        `);
    }
    
    // Update charts with zero data
    updateBindingCharts([]);
}

function initializeBindingCharts() {
    // Vendor distribution chart
    const ctx1 = document.getElementById('vendorChart').getContext('2d');
    window.vendorChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['ABC Binding', 'XYZ Repair', 'Quality Binding', 'Internal Team'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Cost trend chart
    const ctx2 = document.getElementById('costTrendChart').getContext('2d');
    window.costTrendChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Cost (₹)',
                data: [0, 0, 0, 0, 0, 0],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Service cost chart
    const ctx3 = document.getElementById('serviceCostChart').getContext('2d');
    window.serviceCostChart = new Chart(ctx3, {
        type: 'bar',
        data: {
            labels: ['Binding', 'Repair', 'Re-binding', 'Restoration', 'Cleaning'],
            datasets: [{
                label: 'Cost (₹)',
                data: [0, 0, 0, 0, 0],
                backgroundColor: 'rgba(255, 206, 86, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateBindingCharts(data) {
    if (window.vendorChart && data.length === 0) {
        window.vendorChart.data.datasets[0].data = [0, 0, 0, 0];
        window.vendorChart.update();
    }
    
    if (window.costTrendChart && data.length === 0) {
        window.costTrendChart.data.datasets[0].data = [0, 0, 0, 0, 0, 0];
        window.costTrendChart.update();
    }
    
    if (window.serviceCostChart && data.length === 0) {
        window.serviceCostChart.data.datasets[0].data = [0, 0, 0, 0, 0];
        window.serviceCostChart.update();
    }
}

function resetFilters() {
    $('#filterForm')[0].reset();
    const today = new Date();
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(monthAgo.toISOString().split('T')[0]);
    
    generateReport();
}

function newBindingRequest() {
    alert('New binding request form would be implemented here.');
}

function updateJobStatus(jobId, status) {
    if (confirm(`Update job ${jobId} status to ${status}?`)) {
        alert('Job status updated successfully!');
        generateReport();
    }
}

function viewJobDetails(jobId) {
    alert(`Viewing details for job ${jobId}`);
}

function exportReport(format) {
    alert(`Exporting Binding Report as ${format.toUpperCase()}...`);
}
</script>
{% endblock %}
