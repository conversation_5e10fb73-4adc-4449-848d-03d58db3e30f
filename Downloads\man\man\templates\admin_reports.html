{% extends "base.html" %}

{% block title %}Reports - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_gate_management') }}">
                <i class="fas fa-door-open me-2"></i>Gate Management
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success active" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_gate_report') }}">
                <i class="fas fa-door-open me-2"></i>Gate Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar me-3"></i>Library Reports Dashboard</h2>
    <div class="text-muted">
        <i class="fas fa-info-circle me-2"></i>Select any report to view detailed information
    </div>
</div>

<!-- Reports Overview Cards -->
<div class="row mb-4">
    <!-- Library Connection Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-wifi fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">Library Connection</h5>
                <p class="card-text text-muted">Track user login sessions and library access patterns</p>
                <a href="{{ url_for('admin_library_connection_report') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Access Register Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-sign-in-alt fa-3x text-success"></i>
                </div>
                <h5 class="card-title">Access Register</h5>
                <p class="card-text text-muted">Monitor entry/exit logs and visitor tracking</p>
                <a href="{{ url_for('admin_access_register_report') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Bibliography Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-book-open fa-3x text-info"></i>
                </div>
                <h5 class="card-title">Bibliography</h5>
                <p class="card-text text-muted">Generate formatted bibliography and citations</p>
                <a href="{{ url_for('admin_bibliography_report') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Counter Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-calculator fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">Counter Report</h5>
                <p class="card-text text-muted">Daily circulation and transaction summaries</p>
                <a href="{{ url_for('admin_counter_report') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Statistics Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-line fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">Statistics</h5>
                <p class="card-text text-muted">Comprehensive library usage statistics and trends</p>
                <a href="{{ url_for('admin_statistics_report') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Binding Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-tools fa-3x text-secondary"></i>
                </div>
                <h5 class="card-title">Binding Report</h5>
                <p class="card-text text-muted">Book binding and maintenance tracking</p>
                <a href="{{ url_for('admin_binding_report') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Database Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-database fa-3x text-dark"></i>
                </div>
                <h5 class="card-title">Database Report</h5>
                <p class="card-text text-muted">System database status and integrity checks</p>
                <a href="{{ url_for('admin_database_report') }}" class="btn btn-dark btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Member Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-success"></i>
                </div>
                <h5 class="card-title">Member Report</h5>
                <p class="card-text text-muted">Student and staff membership analytics</p>
                <a href="{{ url_for('admin_member_report') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Resource Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-archive fa-3x text-info"></i>
                </div>
                <h5 class="card-title">Resource Report</h5>
                <p class="card-text text-muted">Library resource utilization and availability</p>
                <a href="{{ url_for('admin_resource_report') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- No Dues Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-check-circle fa-3x text-success"></i>
                </div>
                <h5 class="card-title">No Dues Report</h5>
                <p class="card-text text-muted">Student clearance and dues verification</p>
                <a href="{{ url_for('admin_no_dues_report') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- QB Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-money-check-alt fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">QB Report</h5>
                <p class="card-text text-muted">Financial and accounting summaries</p>
                <a href="{{ url_for('admin_qb_report') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Transfer Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exchange-alt fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">Transfer Report</h5>
                <p class="card-text text-muted">Book transfers and inter-library transactions</p>
                <a href="{{ url_for('admin_transfer_report') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Missing Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger"></i>
                </div>
                <h5 class="card-title">Missing Report</h5>
                <p class="card-text text-muted">Lost and missing book tracking</p>
                <a href="{{ url_for('admin_missing_report') }}" class="btn btn-danger btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- News Clipping Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-newspaper fa-3x text-info"></i>
                </div>
                <h5 class="card-title">News Clipping Report</h5>
                <p class="card-text text-muted">News clippings and media coverage tracking</p>
                <a href="{{ url_for('admin_news_clipping_report') }}" class="btn btn-info btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>

    <!-- Gate Entry Report -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-door-open fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">Gate Entry Report</h5>
                <p class="card-text text-muted">Student entry/exit tracking and analytics</p>
                <a href="{{ url_for('admin_gate_report') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-eye me-2"></i>View Report
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Quick Statistics Card -->
    <div class="col-lg-9 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Quick Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-primary mb-1" id="totalBooks">-</h4>
                            <small class="text-muted">Total Books</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-success mb-1" id="activeMembers">-</h4>
                            <small class="text-muted">Active Members</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-warning mb-1" id="currentIssues">-</h4>
                            <small class="text-muted">Current Issues</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <h4 class="text-danger mb-1" id="overdueBooks">-</h4>
                            <small class="text-muted">Overdue Books</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Load real statistics on page load
    loadQuickStatistics();
});

function loadQuickStatistics() {
    // Load real library statistics
    $.ajax({
        url: '/api/admin/quick-statistics',
        method: 'GET',
        success: function(response) {
            $('#totalBooks').text(response.total_books || 0);
            $('#activeMembers').text(response.active_members || 0);
            $('#currentIssues').text(response.current_issues || 0);
            $('#overdueBooks').text(response.overdue_books || 0);
        },
        error: function() {
            $('#totalBooks').text('Error');
            $('#activeMembers').text('Error');
            $('#currentIssues').text('Error');
            $('#overdueBooks').text('Error');
        }
    });
}
</script>
{% endblock %}
