{% extends "base.html" %}

{% block title %}College Management - Admin{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-university me-3 text-info"></i>College Management</h2>
        <p class="text-muted mb-0">Manage colleges and their information</p>
    </div>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCollegeModal">
            <i class="fas fa-plus me-2"></i>Add College
        </button>
        <button class="btn btn-success ms-2" onclick="exportColleges()">
            <i class="fas fa-file-excel me-2"></i>Export
        </button>
    </div>
</div>

<!-- College Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-primary">
                    <i class="fas fa-university fa-2x mb-2"></i>
                </div>
                <h4 class="text-primary" id="totalColleges">{{ colleges|length }}</h4>
                <p class="text-muted mb-0 small">Total Colleges</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-success">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                </div>
                <h4 class="text-success" id="activeColleges">{{ colleges|selectattr('is_active')|list|length }}</h4>
                <p class="text-muted mb-0 small">Active Colleges</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-warning">
                    <i class="fas fa-building fa-2x mb-2"></i>
                </div>
                <h4 class="text-warning" id="totalDepartments">0</h4>
                <p class="text-muted mb-0 small">Total Departments</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card autolib-card text-center">
            <div class="card-body">
                <div class="text-info">
                    <i class="fas fa-users fa-2x mb-2"></i>
                </div>
                <h4 class="text-info" id="totalStudents">0</h4>
                <p class="text-muted mb-0 small">Total Students</p>
            </div>
        </div>
    </div>
</div>

<!-- Colleges Table -->
<div class="card autolib-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Colleges List</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="collegesTable">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>College Name</th>
                        <th>Code</th>
                        <th>Address</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% if colleges %}
                        {% for college in colleges %}
                        <tr>
                            <td>{{ college.id }}</td>
                            <td>{{ college.name }}</td>
                            <td><span class="badge bg-primary">{{ college.code }}</span></td>
                            <td>{{ college.address or 'N/A' }}</td>
                            <td>{{ college.phone or 'N/A' }}</td>
                            <td>{{ college.email or 'N/A' }}</td>
                            <td>
                                {% if college.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary" onclick="editCollege({{ college.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="viewDepartments({{ college.id }})">
                                    <i class="fas fa-building"></i>
                                </button>
                                {% if college.is_active %}
                                    <button class="btn btn-sm btn-outline-warning" onclick="toggleCollegeStatus({{ college.id }}, false)">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                {% else %}
                                    <button class="btn btn-sm btn-outline-success" onclick="toggleCollegeStatus({{ college.id }}, true)">
                                        <i class="fas fa-play"></i>
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>No colleges found. Add your first college to get started!
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add College Modal -->
<div class="modal fade" id="addCollegeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New College</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCollegeForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="collegeName" class="form-label">College Name *</label>
                            <input type="text" class="form-control" id="collegeName" name="collegeName" required>
                        </div>
                        <div class="col-md-6">
                            <label for="collegeCode" class="form-label">College Code *</label>
                            <input type="text" class="form-control" id="collegeCode" name="collegeCode" required>
                            <div class="form-text">Short code (e.g., ENG, MED, LAW)</div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <label for="collegeAddress" class="form-label">Address</label>
                            <textarea class="form-control" id="collegeAddress" name="collegeAddress" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="collegePhone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="collegePhone" name="collegePhone">
                        </div>
                        <div class="col-md-6">
                            <label for="collegeEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="collegeEmail" name="collegeEmail">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addCollege()">
                    <i class="fas fa-plus me-2"></i>Add College
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadStatistics();
});

function loadStatistics() {
    // Update department and student counts
    // This would be replaced with actual API calls
    $('#totalDepartments').text('0');
    $('#totalStudents').text('0');
}

function addCollege() {
    const formData = {
        name: $('#collegeName').val(),
        code: $('#collegeCode').val(),
        address: $('#collegeAddress').val(),
        phone: $('#collegePhone').val(),
        email: $('#collegeEmail').val()
    };
    
    // Validate required fields
    if (!formData.name || !formData.code) {
        alert('Please fill all required fields.');
        return;
    }
    
    // Send API request
    $.ajax({
        url: '/api/colleges',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            alert('College added successfully!');
            $('#addCollegeModal').modal('hide');
            $('#addCollegeForm')[0].reset();
            location.reload();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Error adding college';
            alert(error);
        }
    });
}

function editCollege(collegeId) {
    alert('Edit college functionality would be implemented here.');
}

function viewDepartments(collegeId) {
    window.location.href = `/admin/departments?college_id=${collegeId}`;
}

function toggleCollegeStatus(collegeId, status) {
    const action = status ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this college?`)) {
        alert(`College ${action}d successfully!`);
        location.reload();
    }
}

function exportColleges() {
    alert('Exporting colleges data...');
}
</script>
{% endblock %}
