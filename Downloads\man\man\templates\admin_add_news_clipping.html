{% extends "base.html" %}

{% block title %}Add News Clipping - Admin{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
    }

    .card-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0 !important;
        border: none;
    }

    .form-control, .form-select, .form-control:focus, .form-select:focus {
        border-radius: 0.375rem;
        border: 1px solid #e0e6ed;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .text-danger {
        color: #dc3545 !important;
        font-weight: 600;
    }

    .btn {
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.2s ease-in-out;
        padding: 0.5rem 1.5rem;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
    }

    .btn-outline-secondary {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
    }

    .btn-outline-danger {
        border: 2px solid #dc3545;
        color: #dc3545;
        background: transparent;
    }

    .btn-outline-danger:hover {
        background: #dc3545;
        color: white;
    }

    .form-text {
        font-size: 0.85rem;
        color: #6c757d;
        font-style: italic;
    }

    .required-field {
        position: relative;
    }

    .required-field::after {
        content: "*";
        color: #dc3545;
        font-weight: bold;
        margin-left: 3px;
    }

    textarea.form-control {
        resize: vertical;
        min-height: 100px;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #28a745;
    }

    .section-title {
        color: #28a745;
        font-weight: 700;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .input-group-text {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border: none;
        border-radius: 0.375rem 0 0 0.375rem;
    }

    .character-count {
        font-size: 0.75rem;
        color: #6c757d;
        text-align: right;
        margin-top: 0.25rem;
    }

    .form-floating {
        position: relative;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    }

    .validation-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
    }

    .is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .is-valid {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .page-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #28a745;
    }

    .page-header h2 {
        color: #28a745;
        margin: 0;
        font-weight: 700;
    }

    .form-grid {
        display: grid;
        gap: 1rem;
    }

    @media (min-width: 768px) {
        .form-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .form-grid .full-width {
            grid-column: 1 / -1;
        }
    }
</style>
{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('admin_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_librarians') }}">
                <i class="fas fa-user-tie me-2"></i>Manage Librarians
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_colleges') }}">
                <i class="fas fa-university me-2"></i>Manage Colleges
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_departments') }}">
                <i class="fas fa-building me-2"></i>Manage Departments
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('admin_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_payment_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_management') }}">
                <i class="fas fa-tools me-2"></i>Binding & Maintenance
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_qb_report') }}">
                <i class="fas fa-money-check-alt me-2"></i>QB Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_transfer_report') }}">
                <i class="fas fa-exchange-alt me-2"></i>Transfer Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_missing_report') }}">
                <i class="fas fa-exclamation-triangle me-2"></i>Missing Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('admin_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- System Administration -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#systemSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-server me-2"></i>System
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="systemSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('admin_settings') }}">
                <i class="fas fa-cog me-2"></i>System Settings
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <h2><i class="fas fa-plus me-3"></i>Add News Clipping</h2>
        <a href="{{ url_for('admin_news_clippings') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-newspaper me-2"></i>News Clipping Information</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('admin_add_news_clipping') }}" id="newsClippingForm">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </div>
                <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6 mb-3">
                    <label for="clipping_no" class="form-label">News Clipping No <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                        <input type="text" class="form-control" id="clipping_no" name="clipping_no" required
                               placeholder="e.g., NC001, NC002" maxlength="50">
                        <button class="btn btn-outline-secondary" type="button" id="generateClippingNo">
                            <i class="fas fa-magic"></i> Generate
                        </button>
                    </div>
                    <div class="form-text">Unique identifier for the news clipping</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="newspaper_name" class="form-label">Newspaper Name <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-newspaper"></i></span>
                        <input type="text" class="form-control" id="newspaper_name" name="newspaper_name" required
                               placeholder="e.g., The Hindu, Times of India" maxlength="200">
                    </div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="news_type" class="form-label">News Type <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-tags"></i></span>
                        <select class="form-select" id="news_type" name="news_type" required>
                            <option value="">Select News Type</option>
                            <option value="Academic">Academic</option>
                            <option value="Research">Research</option>
                            <option value="Sports">Sports</option>
                            <option value="Cultural">Cultural</option>
                            <option value="Achievement">Achievement</option>
                            <option value="General">General</option>
                            <option value="Event">Event</option>
                            <option value="Announcement">Announcement</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="date" class="form-label">Date <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        <input type="date" class="form-control" id="date" name="date" required>
                    </div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="pages" class="form-label">Pages <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                        <input type="text" class="form-control" id="pages" name="pages" required
                               placeholder="e.g., 1, 1-3, 5-7" maxlength="50">
                    </div>
                    <div class="form-text">Page numbers where the news appears</div>
                </div>

                <!-- College and Department -->
                <div class="col-md-6 mb-3">
                    <label for="college_id" class="form-label">College <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-university"></i></span>
                        <select class="form-select" id="college_id" name="college_id" required>
                            <option value="">Select College</option>
                            {% for college_id, college_name in colleges %}
                            <option value="{{ college_id }}">{{ college_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-edit me-2"></i>Content Information
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="department_id" class="form-label">Department <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                            <select class="form-select" id="department_id" name="department_id" required disabled>
                                <option value="">Select College First</option>
                            </select>
                        </div>
                    </div>

                    <!-- Keywords -->
                    <div class="col-md-6 mb-3">
                        <label for="keywords" class="form-label">Keywords <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-key"></i></span>
                            <input type="text" class="form-control" id="keywords" name="keywords" required
                                   placeholder="e.g., education, technology, research, innovation">
                        </div>
                        <div class="form-text">Separate keywords with commas</div>
                    </div>

                    <!-- Abstract -->
                    <div class="col-md-12 mb-3">
                        <label for="abstract" class="form-label">Abstract <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="abstract" name="abstract" rows="4" required
                                  placeholder="Brief summary of the news content..." maxlength="1000"></textarea>
                        <div class="character-count">
                            <span id="abstractCount">0</span>/1000 characters
                        </div>
                        <div class="form-text">Provide a concise summary of the news article</div>
                    </div>

                    <!-- Content -->
                    <div class="col-md-12 mb-3">
                        <label for="content" class="form-label">Content <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="8" required
                                  placeholder="Full news content..."></textarea>
                        <div class="character-count">
                            <span id="contentCount">0</span> characters
                        </div>
                        <div class="form-text">Enter the complete news article content</div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save News Clipping
                </button>
                <button type="reset" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>Clear Form
                </button>
                <a href="{{ url_for('admin_news_clippings') }}" class="btn btn-outline-danger">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set today's date as default
    $('#date').val(new Date().toISOString().split('T')[0]);

    // Character counters
    $('#abstract').on('input', function() {
        const count = $(this).val().length;
        $('#abstractCount').text(count);

        if (count > 1000) {
            $(this).addClass('is-invalid');
            $('#abstractCount').parent().addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $('#abstractCount').parent().removeClass('text-danger');
        }
    });

    $('#content').on('input', function() {
        const count = $(this).val().length;
        $('#contentCount').text(count);
    });

    // College/Department dropdown functionality
    $('#college_id').change(function() {
        const collegeId = $(this).val();
        const departmentSelect = $('#department_id');

        if (collegeId) {
            departmentSelect.prop('disabled', false);
            departmentSelect.html('<option value="">Loading departments...</option>');

            $.ajax({
                url: `/api/departments/${collegeId}`,
                method: 'GET',
                success: function(departments) {
                    departmentSelect.html('<option value="">Select Department</option>');
                    departments.forEach(function(dept) {
                        departmentSelect.append(`<option value="${dept.id}">${dept.code} - ${dept.name}</option>`);
                    });
                },
                error: function() {
                    departmentSelect.html('<option value="">Error loading departments</option>');
                }
            });
        } else {
            departmentSelect.prop('disabled', true);
            departmentSelect.html('<option value="">Select College First</option>');
        }
    });

    // Real-time form validation
    function validateField(field, errorMsg) {
        const value = $(field).val().trim();
        if (!value) {
            $(field).addClass('is-invalid');
            return false;
        } else {
            $(field).removeClass('is-invalid').addClass('is-valid');
            return true;
        }
    }

    // Validate required fields on blur
    $('#clipping_no, #newspaper_name, #news_type, #date, #pages, #keywords, #abstract, #content').on('blur', function() {
        validateField(this);
    });

    $('#college_id, #department_id').on('change', function() {
        validateField(this);
    });

    // Form submission validation
    $('#newsClippingForm').on('submit', function(e) {
        let isValid = true;

        // Validate all required fields
        const requiredFields = ['#clipping_no', '#newspaper_name', '#news_type', '#date', '#pages', '#college_id', '#department_id', '#keywords', '#abstract', '#content'];

        requiredFields.forEach(function(field) {
            if (!validateField(field)) {
                isValid = false;
            }
        });

        // Special validation for department
        if ($('#department_id').prop('disabled') || !$('#department_id').val()) {
            $('#department_id').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();

            // Show error message
            const errorAlert = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Please fill in all required fields correctly.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('.card-body').prepend(errorAlert);

            // Scroll to first invalid field
            const firstInvalid = $('.is-invalid').first();
            if (firstInvalid.length) {
                $('html, body').animate({
                    scrollTop: firstInvalid.offset().top - 100
                }, 500);
                firstInvalid.focus();
            }

            return false;
        }

        // Show loading state
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');
    });

    // Auto-generate clipping number
    $('#generateClippingNo').on('click', function() {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const timestamp = Date.now().toString().slice(-4);

        const clippingNo = `NC${year}${month}${day}${timestamp}`;
        $('#clipping_no').val(clippingNo);
        validateField('#clipping_no');
    });
});
</script>
{% endblock %}
