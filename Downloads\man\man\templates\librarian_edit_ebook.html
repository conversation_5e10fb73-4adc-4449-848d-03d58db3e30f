{% extends "base.html" %}

{% block title %}Edit E-Book - Librarian{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link" href="{{ url_for('librarian_books') }}">
    <i class="fas fa-book me-2"></i>Manage Books
</a>
<a class="nav-link active" href="{{ url_for('librarian_ebooks') }}">
    <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
</a>
<a class="nav-link" href="{{ url_for('librarian_students') }}">
    <i class="fas fa-graduation-cap me-2"></i>View Students
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_return_dashboard') }}">
    <i class="fas fa-exchange-alt me-2"></i>Issue/Return Books
</a>
<a class="nav-link" href="{{ url_for('librarian_bulk_books') }}">
    <i class="fas fa-file-upload me-2"></i>Bulk Add Books
</a>
<a class="nav-link" href="{{ url_for('librarian_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-edit me-3"></i>Edit E-Book</h2>
    <div>
        <a href="{{ url_for('librarian_add_ebook') }}" class="btn btn-primary btn-custom me-2">
            <i class="fas fa-plus me-2"></i>Add New E-Book
        </a>
        <a href="{{ url_for('librarian_ebooks') }}" class="btn btn-secondary btn-custom">
            <i class="fas fa-arrow-left me-2"></i>Back to E-Books
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-tablet-alt me-2"></i>E-Book Information</h5>
    </div>
    <div class="card-body">
        <form method="POST" class="row g-3">
            <!-- Basic Information -->
            <div class="col-12">
                <h6 class="text-primary"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="access_no" class="form-label">Access Number <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="access_no" name="access_no" value="{{ ebook.access_no }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="title" name="title" value="{{ ebook.title }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="author" class="form-label">Author <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="author" name="author" value="{{ ebook.author }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="publisher" class="form-label">Publisher <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="publisher" name="publisher" value="{{ ebook.publisher }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="subject" class="form-label">Subject <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="subject" name="subject" value="{{ ebook.subject }}" required>
            </div>
            
            <div class="col-md-6">
                <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                <select class="form-select" id="department" name="department" required>
                    {% for dept_code, dept_name in departments %}
                    <option value="{{ dept_code }}" {{ 'selected' if ebook.department == dept_code }}>{{ dept_code }} - {{ dept_name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                <select class="form-select" id="category" name="category" required>
                    <option value="Textbook" {{ 'selected' if ebook.category == 'Textbook' }}>Textbook</option>
                    <option value="Reference" {{ 'selected' if ebook.category == 'Reference' }}>Reference</option>
                    <option value="Research Paper" {{ 'selected' if ebook.category == 'Research Paper' }}>Research Paper</option>
                    <option value="Journal" {{ 'selected' if ebook.category == 'Journal' }}>Journal</option>
                    <option value="Magazine" {{ 'selected' if ebook.category == 'Magazine' }}>Magazine</option>
                    <option value="Manual" {{ 'selected' if ebook.category == 'Manual' }}>Manual</option>
                    <option value="Guide" {{ 'selected' if ebook.category == 'Guide' }}>Guide</option>
                    <option value="Other" {{ 'selected' if ebook.category == 'Other' }}>Other</option>
                </select>
            </div>
            
            <!-- Digital Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-digital-tachograph me-2"></i>Digital Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="file_format" class="form-label">File Format <span class="text-danger">*</span></label>
                <select class="form-select" id="file_format" name="file_format" required>
                    <option value="PDF" {{ 'selected' if ebook.file_format == 'PDF' }}>PDF</option>
                    <option value="EPUB" {{ 'selected' if ebook.file_format == 'EPUB' }}>EPUB</option>
                    <option value="MOBI" {{ 'selected' if ebook.file_format == 'MOBI' }}>MOBI</option>
                    <option value="DOC" {{ 'selected' if ebook.file_format == 'DOC' }}>DOC</option>
                    <option value="DOCX" {{ 'selected' if ebook.file_format == 'DOCX' }}>DOCX</option>
                    <option value="TXT" {{ 'selected' if ebook.file_format == 'TXT' }}>TXT</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="file_size" class="form-label">File Size</label>
                <input type="text" class="form-control" id="file_size" name="file_size" value="{{ ebook.file_size or '' }}" placeholder="e.g., 5.2 MB">
            </div>
            
            <div class="col-md-6">
                <label for="language" class="form-label">Language</label>
                <select class="form-select" id="language" name="language">
                    <option value="English" {{ 'selected' if ebook.language == 'English' }}>English</option>
                    <option value="Hindi" {{ 'selected' if ebook.language == 'Hindi' }}>Hindi</option>
                    <option value="Tamil" {{ 'selected' if ebook.language == 'Tamil' }}>Tamil</option>
                    <option value="Telugu" {{ 'selected' if ebook.language == 'Telugu' }}>Telugu</option>
                    <option value="Malayalam" {{ 'selected' if ebook.language == 'Malayalam' }}>Malayalam</option>
                    <option value="Kannada" {{ 'selected' if ebook.language == 'Kannada' }}>Kannada</option>
                    <option value="Other" {{ 'selected' if ebook.language == 'Other' }}>Other</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="pages" class="form-label">Number of Pages</label>
                <input type="number" class="form-control" id="pages" name="pages" value="{{ ebook.pages or '' }}" min="1">
            </div>
            
            <div class="col-12">
                <label for="download_url" class="form-label">Download URL/File Path</label>
                <input type="url" class="form-control" id="download_url" name="download_url" value="{{ ebook.download_url or '' }}" placeholder="https://example.com/ebook.pdf">
            </div>
            
            <!-- Additional Information -->
            <div class="col-12 mt-4">
                <h6 class="text-primary"><i class="fas fa-plus-circle me-2"></i>Additional Information</h6>
                <hr>
            </div>
            
            <div class="col-md-6">
                <label for="isbn" class="form-label">ISBN</label>
                <input type="text" class="form-control" id="isbn" name="isbn" value="{{ ebook.isbn or '' }}" placeholder="978-0-123456-78-9">
            </div>
            
            <div class="col-12">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="3" placeholder="Brief description of the e-book content...">{{ ebook.description or '' }}</textarea>
            </div>
            
            <div class="col-12 text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>Update E-Book
                </button>
                <a href="{{ url_for('librarian_ebooks') }}" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
