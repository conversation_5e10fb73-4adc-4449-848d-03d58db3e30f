{% extends "base.html" %}

{% block title %}Database Report - Librarian{% endblock %}

{% block sidebar %}
<!-- Dashboard -->
<a class="nav-link" href="{{ url_for('librarian_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>

<!-- Management Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-info" data-bs-toggle="collapse" href="#managementSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-cogs me-2"></i>Management
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="managementSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_books') }}">
                <i class="fas fa-book me-2"></i>Manage Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_ebooks') }}">
                <i class="fas fa-tablet-alt me-2"></i>Manage E-Books
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_students') }}">
                <i class="fas fa-graduation-cap me-2"></i>Manage Students
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_news_clippings') }}">
                <i class="fas fa-newspaper me-2"></i>News Clippings
            </a>
        </div>
    </div>
</div>

<!-- Circulation Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-primary" data-bs-toggle="collapse" href="#circulationSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-exchange-alt me-2"></i>Circulation
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="circulationSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_circulation_counter') }}">
                <i class="fas fa-desktop me-2"></i>Counter Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_issue_history') }}">
                <i class="fas fa-history me-2"></i>Issue History
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bulk_operations') }}">
                <i class="fas fa-layer-group me-2"></i>Bulk Operations
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_payment_management') }}">
                <i class="fas fa-credit-card me-2"></i>Payment Management
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_fine_management') }}">
                <i class="fas fa-money-bill-wave me-2"></i>Fine Management
            </a>
        </div>
    </div>
</div>

<!-- Reports Section -->
<div class="nav-item">
    <a class="nav-link fw-bold text-success" data-bs-toggle="collapse" href="#reportsSubmenu" role="button" aria-expanded="true">
        <i class="fas fa-chart-bar me-2"></i>Reports
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse show" id="reportsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_library_connection_report') }}">
                <i class="fas fa-wifi me-2"></i>Library Connection
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_access_register_report') }}">
                <i class="fas fa-sign-in-alt me-2"></i>Access Register
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_bibliography_report') }}">
                <i class="fas fa-book-open me-2"></i>Bibliography
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_counter_report') }}">
                <i class="fas fa-calculator me-2"></i>Counter Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_statistics_report') }}">
                <i class="fas fa-chart-line me-2"></i>Statistics
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_binding_report') }}">
                <i class="fas fa-tools me-2"></i>Binding Report
            </a>
            <a class="nav-link py-1 active" href="{{ url_for('librarian_database_report') }}">
                <i class="fas fa-database me-2"></i>Database Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_member_report') }}">
                <i class="fas fa-users me-2"></i>Member Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_resource_report') }}">
                <i class="fas fa-archive me-2"></i>Resource Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_no_dues_report') }}">
                <i class="fas fa-check-circle me-2"></i>No Dues Report
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_news_clipping_report') }}">
                <i class="fas fa-newspaper me-2"></i>News Clipping Report
            </a>
        </div>
    </div>
</div>

<!-- Tools & Utilities -->
<div class="nav-item">
    <a class="nav-link fw-bold text-warning" data-bs-toggle="collapse" href="#toolsSubmenu" role="button" aria-expanded="false">
        <i class="fas fa-tools me-2"></i>Tools
        <i class="fas fa-chevron-down float-end mt-1"></i>
    </a>
    <div class="collapse" id="toolsSubmenu">
        <div class="nav flex-column ms-3">
            <a class="nav-link py-1" href="{{ url_for('librarian_barcode_generator') }}">
                <i class="fas fa-barcode me-2"></i>Barcode Generator
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_label_printer') }}">
                <i class="fas fa-print me-2"></i>Label Printer
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_inventory_check') }}">
                <i class="fas fa-clipboard-check me-2"></i>Inventory Check
            </a>
            <a class="nav-link py-1" href="{{ url_for('librarian_notifications') }}">
                <i class="fas fa-bell me-2"></i>Notifications
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-database me-3 text-primary"></i>Database Report</h2>
        <p class="text-muted mb-0">Monitor database performance and statistics</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
        <button class="btn btn-info ms-2" onclick="refreshData()">
            <i class="fas fa-sync-alt me-2"></i>Refresh
        </button>
    </div>
</div>

<!-- Database Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Records</h6>
                        <h3 class="mb-0" id="totalRecords">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Database Size</h6>
                        <h3 class="mb-0" id="databaseSize">0 MB</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Active Tables</h6>
                        <h3 class="mb-0" id="activeTables">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-table fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Last Backup</h6>
                        <h3 class="mb-0" id="lastBackup">--</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-save fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Table Statistics -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Table Statistics</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="tableStatsTable">
                <thead class="table-dark">
                    <tr>
                        <th>Table Name</th>
                        <th>Record Count</th>
                        <th>Size (MB)</th>
                        <th>Last Updated</th>
                        <th>Growth Rate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="tableStatsBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Query Performance</h5>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Storage Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="storageChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Database Version:</strong></td>
                        <td id="dbVersion">--</td>
                    </tr>
                    <tr>
                        <td><strong>Server Uptime:</strong></td>
                        <td id="serverUptime">--</td>
                    </tr>
                    <tr>
                        <td><strong>Connection Pool:</strong></td>
                        <td id="connectionPool">--</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Cache Hit Ratio:</strong></td>
                        <td id="cacheHitRatio">--</td>
                    </tr>
                    <tr>
                        <td><strong>Average Query Time:</strong></td>
                        <td id="avgQueryTime">--</td>
                    </tr>
                    <tr>
                        <td><strong>Active Connections:</strong></td>
                        <td id="activeConnections">--</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let performanceChart, storageChart;

// Initialize the page
$(document).ready(function() {
    loadDatabaseData();
    initializeCharts();
});

function loadDatabaseData() {
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            overview: {
                totalRecords: 15420,
                databaseSize: 245.6,
                activeTables: 12,
                lastBackup: '2024-01-15'
            },
            tableStats: [
                {
                    name: 'books',
                    recordCount: 5000,
                    size: 85.2,
                    lastUpdated: '2024-01-15 14:30',
                    growthRate: '+2.5%'
                },
                {
                    name: 'students',
                    recordCount: 3500,
                    size: 45.8,
                    lastUpdated: '2024-01-15 12:15',
                    growthRate: '+1.8%'
                },
                {
                    name: 'issues',
                    recordCount: 6920,
                    size: 78.4,
                    lastUpdated: '2024-01-15 15:45',
                    growthRate: '+5.2%'
                }
            ],
            systemInfo: {
                dbVersion: 'PostgreSQL 13.4',
                serverUptime: '15 days, 8 hours',
                connectionPool: '25/100',
                cacheHitRatio: '94.5%',
                avgQueryTime: '12ms',
                activeConnections: 8
            }
        };
        
        updateOverview(mockData.overview);
        populateTableStats(mockData.tableStats);
        updateSystemInfo(mockData.systemInfo);
        updateCharts();
    }, 1000);
}

function updateOverview(data) {
    $('#totalRecords').text(data.totalRecords.toLocaleString());
    $('#databaseSize').text(data.databaseSize + ' MB');
    $('#activeTables').text(data.activeTables);
    $('#lastBackup').text(data.lastBackup);
}

function populateTableStats(data) {
    const tbody = $('#tableStatsBody');
    tbody.empty();
    
    data.forEach(function(table) {
        const row = `
            <tr>
                <td><strong>${table.name}</strong></td>
                <td>${table.recordCount.toLocaleString()}</td>
                <td>${table.size} MB</td>
                <td>${table.lastUpdated}</td>
                <td><span class="badge bg-success">${table.growthRate}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="analyzeTable('${table.name}')">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="optimizeTable('${table.name}')">
                        <i class="fas fa-cog"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function updateSystemInfo(data) {
    $('#dbVersion').text(data.dbVersion);
    $('#serverUptime').text(data.serverUptime);
    $('#connectionPool').text(data.connectionPool);
    $('#cacheHitRatio').text(data.cacheHitRatio);
    $('#avgQueryTime').text(data.avgQueryTime);
    $('#activeConnections').text(data.activeConnections);
}

function initializeCharts() {
    // Performance Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            datasets: [{
                label: 'Query Response Time (ms)',
                data: [8, 12, 15, 18, 14, 10],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // Storage Chart
    const storageCtx = document.getElementById('storageChart').getContext('2d');
    storageChart = new Chart(storageCtx, {
        type: 'doughnut',
        data: {
            labels: ['Books', 'Students', 'Issues', 'Other'],
            datasets: [{
                data: [85.2, 45.8, 78.4, 36.2],
                backgroundColor: [
                    'rgb(255, 99, 132)',
                    'rgb(54, 162, 235)',
                    'rgb(255, 205, 86)',
                    'rgb(75, 192, 192)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function updateCharts() {
    // Charts are already initialized with data
}

function refreshData() {
    loadDatabaseData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function analyzeTable(tableName) {
    alert('Analyze table: ' + tableName);
}

function optimizeTable(tableName) {
    if (confirm('Are you sure you want to optimize table: ' + tableName + '?')) {
        alert('Optimizing table: ' + tableName);
    }
}
</script>
{% endblock %}
