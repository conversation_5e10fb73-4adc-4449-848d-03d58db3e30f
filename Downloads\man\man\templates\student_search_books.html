{% extends "base.html" %}

{% block title %}Search Books - Student{% endblock %}

{% block sidebar %}
<a class="nav-link" href="{{ url_for('student_dashboard') }}">
    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
</a>
<a class="nav-link active" href="{{ url_for('student_search_books') }}">
    <i class="fas fa-search me-2"></i>Search Books
</a>
<a class="nav-link" href="{{ url_for('student_issue_history') }}">
    <i class="fas fa-history me-2"></i>Issue History
</a>
<a class="nav-link" href="{{ url_for('student_profile') }}">
    <i class="fas fa-user me-2"></i>My Profile
</a>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-search me-3"></i>Search Books</h2>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Search & Filter</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('student_search_books') }}">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label for="search" class="form-label">
                        <i class="fas fa-search me-2"></i>Search by Title or Author
                    </label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request.args.get('search', '') }}" 
                           placeholder="Enter book title or author name">
                </div>
                
                <div class="col-md-4 mb-3">
                    <label for="category" class="form-label">
                        <i class="fas fa-tags me-2"></i>Category
                    </label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for cat in categories %}
                        <option value="{{ cat }}" {% if request.args.get('category') == cat %}selected{% endif %}>
                            {{ cat }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-custom me-2">
                    <i class="fas fa-search me-2"></i>Search Books
                </button>
                <a href="{{ url_for('student_search_books') }}" class="btn btn-secondary btn-custom">
                    <i class="fas fa-times me-2"></i>Clear Filter
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Search Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-book me-2"></i>
            {% if request.args.get('search') or request.args.get('category') %}
                Search Results ({{ books|length }} found)
            {% else %}
                All Books ({{ books|length }} total)
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if books %}
        <div class="row">
            {% for book in books %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 book-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">{{ book.title }}</h6>
                            <span class="badge {% if book.available_count > 0 %}bg-success{% else %}bg-danger{% endif %}">
                                {% if book.available_count > 0 %}Available{% else %}Not Available{% endif %}
                            </span>
                        </div>
                        
                        <p class="card-text text-muted mb-2">
                            <i class="fas fa-user me-1"></i>{{ book.author }}
                        </p>
                        
                        <p class="card-text">
                            <span class="badge bg-secondary">{{ book.category }}</span>
                        </p>
                        
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <small class="text-muted">Total Copies</small>
                                <div class="fw-bold">{{ book.quantity }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Available</small>
                                <div class="fw-bold {% if book.available_count > 0 %}text-success{% else %}text-danger{% endif %}">
                                    {{ book.available_count }}
                                </div>
                            </div>
                        </div>
                        
                        {% if book.available_count > 0 %}
                        <div class="mt-3">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Available for issue - Visit librarian to borrow
                            </small>
                        </div>
                        {% else %}
                        <div class="mt-3">
                            <small class="text-danger">
                                <i class="fas fa-times-circle me-1"></i>
                                All copies currently issued
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            {% if request.args.get('search') or request.args.get('category') %}
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No books found</h5>
                <p class="text-muted">Try adjusting your search criteria.</p>
                <a href="{{ url_for('student_search_books') }}" class="btn btn-primary">
                    <i class="fas fa-list me-2"></i>View All Books
                </a>
            {% else %}
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No books available</h5>
                <p class="text-muted">The library catalog is empty.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- How to Issue Books -->
<div class="alert alert-info mt-4">
    <h6><i class="fas fa-info-circle me-2"></i>How to Issue Books</h6>
    <p class="mb-0">
        To issue a book, visit the librarian with your student ID and request the book you want to borrow. 
        Books are issued for 14 days with a fine of $2.00 per day for late returns.
    </p>
</div>
{% endblock %}

{% block extra_css %}
<style>
.book-card {
    transition: transform 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.book-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Live search functionality
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // Auto-submit form after 500ms of no typing
            if ($('#search').val().length >= 3 || $('#search').val().length === 0) {
                $('form').submit();
            }
        }, 500);
    });
    
    // Focus on search input when page loads
    $('#search').focus();
});
</script>
{% endblock %}
