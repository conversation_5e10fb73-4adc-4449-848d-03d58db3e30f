{% extends "base.html" %}

{% block title %}QB Report - Librarian{% endblock %}

{% block sidebar %}
{% include 'librarian_sidebar.html' %}
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2><i class="fas fa-money-check-alt me-3 text-primary"></i>QB Report</h2>
        <p class="text-muted mb-0">Financial and accounting reports for library operations</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="exportReport('excel')">
            <i class="fas fa-file-excel me-2"></i>Export Excel
        </button>
        <button class="btn btn-danger ms-2" onclick="exportReport('pdf')">
            <i class="fas fa-file-pdf me-2"></i>Export PDF
        </button>
    </div>
</div>

<!-- Filter Section -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">From Date</label>
                <input type="date" class="form-control" id="dateFrom">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">To Date</label>
                <input type="date" class="form-control" id="dateTo">
            </div>
            <div class="col-md-3">
                <label for="transactionType" class="form-label">Transaction Type</label>
                <select class="form-select" id="transactionType">
                    <option value="">All Types</option>
                    <option value="fine">Fines</option>
                    <option value="membership">Membership</option>
                    <option value="deposit">Security Deposit</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="paymentMethod" class="form-label">Payment Method</label>
                <select class="form-select" id="paymentMethod">
                    <option value="">All Methods</option>
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="online">Online</option>
                    <option value="cheque">Cheque</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-search me-2"></i>Apply Filters
                </button>
                <button class="btn btn-secondary ms-2" onclick="resetFilters()">
                    <i class="fas fa-undo me-2"></i>Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Revenue</h6>
                        <h3 class="mb-0" id="totalRevenue">₹0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-rupee-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Fines Collected</h6>
                        <h3 class="mb-0" id="finesCollected">₹0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Pending Dues</h6>
                        <h3 class="mb-0" id="pendingDues">₹0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Transactions</h6>
                        <h3 class="mb-0" id="totalTransactions">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Financial Transactions</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead class="table-dark">
                    <tr>
                        <th>Transaction ID</th>
                        <th>Date</th>
                        <th>Student ID</th>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Payment Method</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="transactionsTableBody">
                    <!-- Data will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Initialize the page
$(document).ready(function() {
    loadTransactionData();
    
    // Set default dates (last 30 days)
    const today = new Date();
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    $('#dateTo').val(today.toISOString().split('T')[0]);
    $('#dateFrom').val(lastMonth.toISOString().split('T')[0]);
});

function loadTransactionData() {
    // Show loading state
    $('#transactionsTableBody').html('<tr><td colspan="9" class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</td></tr>');
    
    // Simulate API call with mock data
    setTimeout(function() {
        const mockData = {
            summary: {
                totalRevenue: 25000,
                finesCollected: 8500,
                pendingDues: 3200,
                totalTransactions: 156
            },
            data: [
                {
                    id: '1',
                    transactionId: 'TXN001',
                    date: '2024-01-15',
                    studentId: 'STU001',
                    name: 'John Doe',
                    type: 'fine',
                    amount: 50,
                    paymentMethod: 'cash',
                    status: 'completed'
                },
                {
                    id: '2',
                    transactionId: 'TXN002',
                    date: '2024-01-14',
                    studentId: 'STU002',
                    name: 'Jane Smith',
                    type: 'membership',
                    amount: 500,
                    paymentMethod: 'online',
                    status: 'completed'
                }
            ]
        };
        
        updateSummary(mockData.summary);
        populateTable(mockData.data);
    }, 1000);
}

function updateSummary(data) {
    $('#totalRevenue').text('₹' + data.totalRevenue.toLocaleString());
    $('#finesCollected').text('₹' + data.finesCollected.toLocaleString());
    $('#pendingDues').text('₹' + data.pendingDues.toLocaleString());
    $('#totalTransactions').text(data.totalTransactions);
}

function populateTable(data) {
    const tbody = $('#transactionsTableBody');
    tbody.empty();
    
    if (data.length === 0) {
        tbody.html('<tr><td colspan="9" class="text-center">No transactions found</td></tr>');
        return;
    }
    
    data.forEach(function(transaction) {
        const row = `
            <tr>
                <td><strong>${transaction.transactionId}</strong></td>
                <td>${transaction.date}</td>
                <td>${transaction.studentId}</td>
                <td>${transaction.name}</td>
                <td><span class="badge bg-${getTypeColor(transaction.type)}">${transaction.type}</span></td>
                <td>₹${transaction.amount}</td>
                <td><span class="badge bg-${getPaymentMethodColor(transaction.paymentMethod)}">${transaction.paymentMethod}</span></td>
                <td><span class="badge bg-${getStatusColor(transaction.status)}">${transaction.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewTransaction('${transaction.id}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="printReceipt('${transaction.id}')">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getTypeColor(type) {
    const colors = {
        'fine': 'warning',
        'membership': 'primary',
        'deposit': 'info',
        'other': 'secondary'
    };
    return colors[type] || 'secondary';
}

function getPaymentMethodColor(method) {
    const colors = {
        'cash': 'success',
        'card': 'primary',
        'online': 'info',
        'cheque': 'warning'
    };
    return colors[method] || 'secondary';
}

function getStatusColor(status) {
    const colors = {
        'completed': 'success',
        'pending': 'warning',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function applyFilters() {
    loadTransactionData();
}

function resetFilters() {
    $('#dateFrom').val('');
    $('#dateTo').val('');
    $('#transactionType').val('');
    $('#paymentMethod').val('');
    loadTransactionData();
}

function exportReport(format) {
    alert('Export functionality would be implemented here for format: ' + format);
}

function viewTransaction(transactionId) {
    alert('View transaction details: ' + transactionId);
}

function printReceipt(transactionId) {
    alert('Print receipt for transaction: ' + transactionId);
}
</script>
{% endblock %}
