<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Library Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 700;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #495057;
        }
        .btn-custom {
            border-radius: 4px;
            padding: 10px 16px;
            font-weight: 500;
            margin: 5px;
        }
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            width: 200px;
            height: calc(100vh - 56px);
            background-color: white;
            border-right: 1px solid #e9ecef;
            box-shadow: 2px 0 4px rgba(0,0,0,0.05);
            overflow-y: auto;
            z-index: 1000;
        }
        .sidebar .nav-link {
            color: #495057;
            padding: 8px 12px;
            margin-bottom: 1px;
            border-radius: 0;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            font-size: 0.85rem;
        }
        .sidebar .nav-link:hover {
            background-color: #f8f9fa;
            color: #0d6efd;
            border-left-color: #0d6efd;
        }
        .sidebar .nav-link.active {
            background-color: #e7f1ff;
            color: #0d6efd;
            border-left-color: #0d6efd;
            font-weight: 600;
        }
        .content-area {
            padding: 30px;
            background-color: #f8f9fa;
        }
        .stat-card {
            border-left: 4px solid;
            background: white;
            transition: transform 0.2s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .stat-card-primary { border-left-color: #0d6efd; }
        .stat-card-success { border-left-color: #198754; }
        .stat-card-warning { border-left-color: #ffc107; }
        .stat-card-danger { border-left-color: #dc3545; }
        .stat-card-info { border-left-color: #0dcaf0; }
        .table {
            background-color: white;
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
        }
        .table td {
            vertical-align: middle;
        }
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .alert {
            border-radius: 4px;
            border: none;
        }
        .badge {
            font-weight: 500;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #495057;
        }

        /* AutoLib Style Quick Actions */
        .quick-action-btn {
            transition: all 0.3s ease;
            border: 2px solid;
            min-height: 120px;
            text-decoration: none;
        }
        .quick-action-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .quick-action-btn i {
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover i {
            transform: scale(1.1);
        }

        /* AutoLib Style Cards */
        .autolib-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .autolib-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        /* Modern Gradient Backgrounds */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .bg-gradient-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .bg-gradient-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .bg-gradient-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* Status Indicators */
        .status-online {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Modern Navigation */
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dropdown-menu {
            border: none;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            border-radius: 8px;
        }
        .dropdown-item {
            padding: 12px 20px;
            transition: all 0.2s ease;
        }
        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
        <div class="container-fluid">
            <a class="navbar-brand text-primary" href="#">
                <i class="fas fa-book-open me-2"></i>
                Smart Library Management
            </a>

            <!-- Global Search -->
            {% if session.user_role %}
            <div class="d-flex flex-grow-1 mx-4">
                <div class="position-relative w-100" style="max-width: 500px;">
                    <input type="text" class="form-control" id="globalSearch"
                           placeholder="Search books, e-books, students..." autocomplete="off">
                    <div id="searchResults" class="position-absolute w-100 bg-white border rounded shadow-sm mt-1"
                         style="z-index: 1050; max-height: 400px; overflow-y: auto; display: none;"></div>
                </div>
            </div>
            {% endif %}



            <div class="navbar-nav ms-auto">
                {% if session.user_name %}
                    <span class="navbar-text me-3 text-muted">
                        <i class="fas fa-user me-2"></i>{{ session.user_name }}
                        {% if session.user_role %}
                        <span class="badge bg-primary ms-2">{{ session.user_role.title() }}</span>
                        {% endif %}
                    </span>
                    <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            {% if session.user_role %}
            <div class="col-md-2 col-lg-2 px-0" style="width: 200px;">
                <div class="sidebar" style="width: 200px;">
                    <nav class="nav flex-column p-2">
                        {% block sidebar %}{% endblock %}
                    </nav>
                </div>
            </div>
            {% endif %}

            <!-- Main Content -->
            <div class="{% if session.user_role %}col-md-10 col-lg-10{% else %}col-12{% endif %}" style="{% if session.user_role %}margin-left: 200px;{% endif %}">
                <div class="content-area" style="{% if session.user_role %}margin-left: 0; padding: 20px; height: calc(100vh - 56px); overflow-y: auto;{% endif %}">
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ messages[0] }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Global Search JavaScript -->
    <script>
    $(document).ready(function() {
        let searchTimeout;

        // Global search functionality
        $('#globalSearch').on('input', function() {
            const query = $(this).val().trim();

            clearTimeout(searchTimeout);

            if (query.length >= 2) {
                searchTimeout = setTimeout(function() {
                    performGlobalSearch(query);
                }, 300);
            } else {
                $('#searchResults').hide();
            }
        });

        function performGlobalSearch(query) {
            $.get('/api/global_search', { q: query }, function(data) {
                const resultsContainer = $('#searchResults');
                resultsContainer.empty();

                if (data.length > 0) {
                    data.forEach(function(item) {
                        const resultItem = $(`
                            <div class="search-result-item p-3 border-bottom" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="${item.icon} text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold text-dark">${item.title}</div>
                                        <div class="text-muted small">${item.subtitle}</div>
                                        <div class="text-muted small">
                                            <span class="badge bg-secondary">${item.category}</span>
                                            <span class="ms-2">Access: ${item.access_no}</span>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        ${item.available ?
                                            '<span class="badge bg-success">Available</span>' :
                                            '<span class="badge bg-danger">Not Available</span>'
                                        }
                                    </div>
                                </div>
                            </div>
                        `);

                        resultItem.hover(function() {
                            $(this).addClass('bg-light');
                        }, function() {
                            $(this).removeClass('bg-light');
                        });

                        resultItem.click(function() {
                            $('#globalSearch').val(item.title);
                            $('#searchResults').hide();
                            // You can add navigation logic here if needed
                        });

                        resultsContainer.append(resultItem);
                    });

                    resultsContainer.show();
                } else {
                    resultsContainer.html('<div class="p-3 text-muted text-center">No results found</div>').show();
                }
            }).fail(function() {
                $('#searchResults').html('<div class="p-3 text-danger text-center">Search error occurred</div>').show();
            });
        }

        // Hide search results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#globalSearch, #searchResults').length) {
                $('#searchResults').hide();
            }
        });

        // Clear search on escape
        $('#globalSearch').on('keydown', function(e) {
            if (e.key === 'Escape') {
                $(this).val('');
                $('#searchResults').hide();
            }
        });

        // Collapsible sidebar functionality
        $('.nav-link[data-bs-toggle="collapse"]').on('click', function(e) {
            e.preventDefault();

            const target = $(this).attr('href');
            const chevron = $(this).find('.fa-chevron-down, .fa-chevron-right');

            // Toggle the target collapse
            $(target).collapse('toggle');

            // Update chevron icon
            $(target).on('shown.bs.collapse', function() {
                chevron.removeClass('fa-chevron-right').addClass('fa-chevron-down');
            });

            $(target).on('hidden.bs.collapse', function() {
                chevron.removeClass('fa-chevron-down').addClass('fa-chevron-right');
            });
        });

        // Initialize sidebar state - all sections expanded by default
        $('.collapse').addClass('show');
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
